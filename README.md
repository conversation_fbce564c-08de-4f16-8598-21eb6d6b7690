# jelly 落地成渣

#### 介绍
- cocos creator项目
- 构建开发为微信小程序
- 小程序码：![image](https://gitee.com/cying314/jelly-floor-slagging/raw/master/images/QRcode.jpg)
- 项目框架技术：微信小游戏、微信云开发、微信小游戏开放数据域、Cocos Creator
- 作品介绍：一款以果冻为主题的休闲游戏。玩家通过划线创建“平台”与果冻进行碰撞，阻止果冻落入地面或碰撞到障碍物。
- 技术方案：
  - 使用 cocos creator 游戏引擎构建开发；
  - 依赖微信云存储实现游戏资源加载，每次进入游戏执行检查本地资源，若存在缺失资源，则通过云存储 API 进行下载，下载完成后将资源保存在本地缓存；
  - 利用微信云开发存储游戏内的用户数据，排行榜相关数据则是上传托管到微信开放数据；
  - 游戏内排行榜利用“开放数据域”渲染，开放数据域通过cocos 提供的WXSubContextView 组件引入主域；

#### 说明

1.  `/mainProject`  为主域程序项目
2.  `/wxSub`  为排行榜子域项目

#### 项目截图

1. **加载页面**

  ![image](https://gitee.com/cying314/jelly-floor-slagging/raw/master/images/01.jpg)

2. **首页**

  ![image](https://gitee.com/cying314/jelly-floor-slagging/raw/master/images/02.jpg)

3. **游戏界面**

  ![image](https://gitee.com/cying314/jelly-floor-slagging/raw/master/images/03.jpg)

4. **换肤界面**

  ![image](https://gitee.com/cying314/jelly-floor-slagging/raw/master/images/04.jpg)

5. **排行榜**

  ![image](https://gitee.com/cying314/jelly-floor-slagging/raw/master/images/05.jpg)

6. **游戏帮助**

  ![image](https://gitee.com/cying314/jelly-floor-slagging/raw/master/images/06.jpg)


