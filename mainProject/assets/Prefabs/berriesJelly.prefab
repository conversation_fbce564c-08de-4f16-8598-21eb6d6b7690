[{"__type__": "cc.Prefab", "_name": "", "_objFlags": 0, "_native": "", "data": {"__id__": 1}, "optimizationPolicy": 0, "asyncLoadAssets": false, "readonly": false}, {"__type__": "cc.Node", "_name": "berriesJelly", "_objFlags": 0, "_parent": null, "_children": [{"__id__": 2}], "_active": true, "_components": [{"__id__": 4}, {"__id__": 5}], "_prefab": {"__id__": 6}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 90, "height": 105}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.4}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 1, "groupIndex": 1, "_id": ""}, {"__type__": "cc.Node", "_name": "broken", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [], "_active": true, "_components": [], "_prefab": {"__id__": 3}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 270, "height": 282}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.492, "y": 0.458}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 1, "groupIndex": 1, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "bec5dd3b-fa74-4e0d-ba2c-c6fdb9e944b5"}, "fileId": "a8ZZHRyaNCY4n/XI/YbUrx", "sync": false}, {"__type__": "cc.RigidBody", "_name": "", "_objFlags": 0, "node": {"__id__": 1}, "_enabled": true, "_type": 2, "_allowSleep": false, "_gravityScale": 3, "_linearDamping": 0, "_angularDamping": 1, "_linearVelocity": {"__type__": "cc.Vec2", "x": 0, "y": -300}, "_angularVelocity": 0, "_fixedRotation": false, "enabledContactListener": true, "bullet": false, "awakeOnLoad": true, "_id": ""}, {"__type__": "cc.PhysicsPolygonCollider", "_name": "", "_objFlags": 0, "node": {"__id__": 1}, "_enabled": true, "tag": 66, "_density": 1, "_sensor": false, "_friction": 0.2, "_restitution": 0, "body": null, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "points": [{"__type__": "cc.Vec2", "x": -10.1, "y": 38.6}, {"__type__": "cc.Vec2", "x": -23.4, "y": 32.9}, {"__type__": "cc.Vec2", "x": -34.68, "y": 21.95}, {"__type__": "cc.Vec2", "x": -42.31, "y": 6.16}, {"__type__": "cc.Vec2", "x": -45, "y": -8.1}, {"__type__": "cc.Vec2", "x": -43.4, "y": -22.41}, {"__type__": "cc.Vec2", "x": -38.33, "y": -32.23}, {"__type__": "cc.Vec2", "x": -29.97, "y": -38.06}, {"__type__": "cc.Vec2", "x": -18.89, "y": -40.7}, {"__type__": "cc.Vec2", "x": 2.18, "y": -41.94}, {"__type__": "cc.Vec2", "x": 21.13, "y": -40.93}, {"__type__": "cc.Vec2", "x": 33.2, "y": -38}, {"__type__": "cc.Vec2", "x": 42.8, "y": -27.6}, {"__type__": "cc.Vec2", "x": 44.9, "y": -11.7}, {"__type__": "cc.Vec2", "x": 43.2, "y": 2.5}, {"__type__": "cc.Vec2", "x": 38.3, "y": 15.7}, {"__type__": "cc.Vec2", "x": 30.89, "y": 25.7}, {"__type__": "cc.Vec2", "x": 19.62, "y": 34.56}, {"__type__": "cc.Vec2", "x": 7.14, "y": 38.93}, {"__type__": "cc.Vec2", "x": -1.93, "y": 39.22}], "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "bec5dd3b-fa74-4e0d-ba2c-c6fdb9e944b5"}, "fileId": "ceFvT/o/hODLZRmQmXXpvC", "sync": false}]