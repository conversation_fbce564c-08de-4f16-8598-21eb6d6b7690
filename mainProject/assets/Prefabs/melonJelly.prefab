[{"__type__": "cc.Prefab", "_name": "", "_objFlags": 0, "_native": "", "data": {"__id__": 1}, "optimizationPolicy": 0, "asyncLoadAssets": false, "readonly": false}, {"__type__": "cc.Node", "_name": "melonJelly", "_objFlags": 0, "_parent": null, "_children": [{"__id__": 2}], "_active": true, "_components": [{"__id__": 4}, {"__id__": 5}], "_prefab": {"__id__": 6}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 80, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.38}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 1, "groupIndex": 1, "_id": ""}, {"__type__": "cc.Node", "_name": "broken", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [], "_active": true, "_components": [], "_prefab": {"__id__": 3}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 258, "height": 250}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.505, "y": 0.495}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 1, "groupIndex": 1, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "0c4acb99-62ca-4738-9245-a321a04147bb"}, "fileId": "aco3JY0SdKuLnSUIjux0vV", "sync": false}, {"__type__": "cc.RigidBody", "_name": "", "_objFlags": 0, "node": {"__id__": 1}, "_enabled": true, "_type": 2, "_allowSleep": false, "_gravityScale": 3, "_linearDamping": 0, "_angularDamping": 1, "_linearVelocity": {"__type__": "cc.Vec2", "x": 0, "y": -300}, "_angularVelocity": 0, "_fixedRotation": false, "enabledContactListener": true, "bullet": false, "awakeOnLoad": true, "_id": ""}, {"__type__": "cc.PhysicsPolygonCollider", "_name": "", "_objFlags": 0, "node": {"__id__": 1}, "_enabled": true, "tag": 66, "_density": 1, "_sensor": false, "_friction": 0.2, "_restitution": 0, "body": null, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "points": [{"__type__": "cc.Vec2", "x": -37.38, "y": 39.1}, {"__type__": "cc.Vec2", "x": -40, "y": 36.07}, {"__type__": "cc.Vec2", "x": -39.93, "y": -33.57}, {"__type__": "cc.Vec2", "x": -35.97, "y": -37.92}, {"__type__": "cc.Vec2", "x": 35.87, "y": -37.87}, {"__type__": "cc.Vec2", "x": 39.5, "y": -34.3}, {"__type__": "cc.Vec2", "x": 39.2, "y": 35.3}, {"__type__": "cc.Vec2", "x": 35.7, "y": 39.2}], "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "0c4acb99-62ca-4738-9245-a321a04147bb"}, "fileId": "ceFvT/o/hODLZRmQmXXpvC", "sync": false}]