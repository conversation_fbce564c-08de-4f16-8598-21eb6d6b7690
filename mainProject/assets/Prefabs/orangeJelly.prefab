[{"__type__": "cc.Prefab", "_name": "", "_objFlags": 0, "_native": "", "data": {"__id__": 1}, "optimizationPolicy": 0, "asyncLoadAssets": false, "readonly": false}, {"__type__": "cc.Node", "_name": "orangeJelly", "_objFlags": 0, "_parent": null, "_children": [{"__id__": 2}], "_active": true, "_components": [{"__id__": 4}, {"__id__": 5}], "_prefab": {"__id__": 6}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 80, "height": 110}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.45}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 1, "groupIndex": 1, "_id": ""}, {"__type__": "cc.Node", "_name": "broken", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [], "_active": true, "_components": [], "_prefab": {"__id__": 3}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 248, "height": 245}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.492, "y": 0.484}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 1, "groupIndex": 1, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "39aa3e4b-7470-492b-82be-2670c42260d3"}, "fileId": "1cHZd2okpBR4stiTzdRrL0", "sync": false}, {"__type__": "cc.RigidBody", "_name": "", "_objFlags": 0, "node": {"__id__": 1}, "_enabled": true, "_type": 2, "_allowSleep": false, "_gravityScale": 3, "_linearDamping": 0, "_angularDamping": 1, "_linearVelocity": {"__type__": "cc.Vec2", "x": 0, "y": -300}, "_angularVelocity": 0, "_fixedRotation": false, "enabledContactListener": true, "bullet": false, "awakeOnLoad": true, "_id": ""}, {"__type__": "cc.PhysicsPolygonCollider", "_name": "", "_objFlags": 0, "node": {"__id__": 1}, "_enabled": true, "tag": 66, "_density": 1, "_sensor": false, "_friction": 0.2, "_restitution": 0, "body": null, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "points": [{"__type__": "cc.Vec2", "x": -3.38, "y": 49.7}, {"__type__": "cc.Vec2", "x": -7.13, "y": 47.05}, {"__type__": "cc.Vec2", "x": -8.72, "y": 42.85}, {"__type__": "cc.Vec2", "x": -8.06, "y": 38.2}, {"__type__": "cc.Vec2", "x": -6.1, "y": 35.9}, {"__type__": "cc.Vec2", "x": -17.4, "y": 33.7}, {"__type__": "cc.Vec2", "x": -23.2, "y": 27.9}, {"__type__": "cc.Vec2", "x": -31.2, "y": 16.2}, {"__type__": "cc.Vec2", "x": -36.6, "y": 0.1}, {"__type__": "cc.Vec2", "x": -39.7, "y": -18.4}, {"__type__": "cc.Vec2", "x": -39.4, "y": -28.2}, {"__type__": "cc.Vec2", "x": -37.4, "y": -36.4}, {"__type__": "cc.Vec2", "x": -33.1, "y": -41.6}, {"__type__": "cc.Vec2", "x": -24.1, "y": -46}, {"__type__": "cc.Vec2", "x": -6, "y": -48.6}, {"__type__": "cc.Vec2", "x": 18.8, "y": -48.1}, {"__type__": "cc.Vec2", "x": 30.1, "y": -45.9}, {"__type__": "cc.Vec2", "x": 35.9, "y": -42.1}, {"__type__": "cc.Vec2", "x": 39.4, "y": -35.1}, {"__type__": "cc.Vec2", "x": 39.8, "y": -24.1}, {"__type__": "cc.Vec2", "x": 37.91, "y": -6.91}, {"__type__": "cc.Vec2", "x": 30.9, "y": 14.77}, {"__type__": "cc.Vec2", "x": 25.82, "y": 24.04}, {"__type__": "cc.Vec2", "x": 20.72, "y": 30.28}, {"__type__": "cc.Vec2", "x": 14.48, "y": 33.87}, {"__type__": "cc.Vec2", "x": 7.48, "y": 35.35}, {"__type__": "cc.Vec2", "x": 9.88, "y": 39.41}, {"__type__": "cc.Vec2", "x": 9.74, "y": 44.39}, {"__type__": "cc.Vec2", "x": 6.73, "y": 48.47}, {"__type__": "cc.Vec2", "x": 1.82, "y": 50.42}], "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "39aa3e4b-7470-492b-82be-2670c42260d3"}, "fileId": "ceFvT/o/hODLZRmQmXXpvC", "sync": false}]