[{"__type__": "cc.Prefab", "_name": "", "_objFlags": 0, "_native": "", "data": {"__id__": 1}, "optimizationPolicy": 0, "asyncLoadAssets": false, "readonly": false}, {"__type__": "cc.Node", "_name": "peachJelly", "_objFlags": 0, "_parent": null, "_children": [{"__id__": 2}], "_active": true, "_components": [{"__id__": 4}, {"__id__": 5}], "_prefab": {"__id__": 6}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 105}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.45, "y": 0.43}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 1, "groupIndex": 1, "_id": ""}, {"__type__": "cc.Node", "_name": "broken", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [], "_active": true, "_components": [], "_prefab": {"__id__": 3}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 319, "height": 315}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.501}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 1, "groupIndex": 1, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "d00bc5eb-3706-485e-8f23-1e68a041fcec"}, "fileId": "d3rBmn2PdIJKAjwh+QN74c", "sync": false}, {"__type__": "cc.RigidBody", "_name": "", "_objFlags": 0, "node": {"__id__": 1}, "_enabled": true, "_type": 2, "_allowSleep": false, "_gravityScale": 3, "_linearDamping": 0, "_angularDamping": 1, "_linearVelocity": {"__type__": "cc.Vec2", "x": 0, "y": -300}, "_angularVelocity": 0, "_fixedRotation": false, "enabledContactListener": true, "bullet": false, "awakeOnLoad": true, "_id": ""}, {"__type__": "cc.PhysicsPolygonCollider", "_name": "", "_objFlags": 0, "node": {"__id__": 1}, "_enabled": true, "tag": 66, "_density": 1, "_sensor": false, "_friction": 0.2, "_restitution": 0, "body": null, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "points": [{"__type__": "cc.Vec2", "x": 12.9, "y": 42.3}, {"__type__": "cc.Vec2", "x": 1.3, "y": 44.3}, {"__type__": "cc.Vec2", "x": -9.444444444444443, "y": 43.155439330543935}, {"__type__": "cc.Vec2", "x": -25, "y": 37.2}, {"__type__": "cc.Vec2", "x": -35.22222222222222, "y": 27.778870292887028}, {"__type__": "cc.Vec2", "x": -41, "y": 18.113598326359828}, {"__type__": "cc.Vec2", "x": -44.44, "y": 6.33}, {"__type__": "cc.Vec2", "x": -44.84, "y": -4.48}, {"__type__": "cc.Vec2", "x": -42.77777777777778, "y": -15.275523012552302}, {"__type__": "cc.Vec2", "x": -36.59, "y": -27.03}, {"__type__": "cc.Vec2", "x": -28.47, "y": -35.27}, {"__type__": "cc.Vec2", "x": -17.36, "y": -42.04}, {"__type__": "cc.Vec2", "x": -5.68, "y": -44.33}, {"__type__": "cc.Vec2", "x": 6.18, "y": -44.6}, {"__type__": "cc.Vec2", "x": 18.55555555555555, "y": -41.635355648535565}, {"__type__": "cc.Vec2", "x": 27.444444444444443, "y": -36.36338912133891}, {"__type__": "cc.Vec2", "x": 35.44444444444444, "y": -28.894769874476985}, {"__type__": "cc.Vec2", "x": 41.8, "y": -18.3}, {"__type__": "cc.Vec2", "x": 45.222222222222214, "y": -4.731589958158999}, {"__type__": "cc.Vec2", "x": 43.3, "y": 12.9}, {"__type__": "cc.Vec2", "x": 37.6, "y": 24.7}, {"__type__": "cc.Vec2", "x": 27.5, "y": 35}], "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "d00bc5eb-3706-485e-8f23-1e68a041fcec"}, "fileId": "ceFvT/o/hODLZRmQmXXpvC", "sync": false}]