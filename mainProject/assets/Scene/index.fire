[{"__type__": "cc.SceneAsset", "_name": "", "_objFlags": 0, "_native": "", "scene": {"__id__": 1}}, {"__type__": "cc.Scene", "_objFlags": 0, "_parent": null, "_children": [{"__id__": 2}, {"__id__": 4}], "_active": true, "_components": [], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_is3DNode": true, "_groupIndex": 0, "groupIndex": 0, "autoReleaseAssets": false, "_id": "f4e480ce-01d8-40c9-a271-26e6def43201"}, {"__type__": "cc.Node", "_name": "audios", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [], "_active": true, "_components": [{"__id__": 3}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "18Cdn8yTRDrqT5QdpcIMu/"}, {"__type__": "4323ejYUl9L6aw8e568F7NZ", "_name": "", "_objFlags": 0, "node": {"__id__": 2}, "_enabled": true, "_id": "d7qdnqvS5As7pYX64kBnTv"}, {"__type__": "cc.Node", "_name": "<PERSON><PERSON>", "_objFlags": 512, "_parent": {"__id__": 1}, "_children": [{"__id__": 5}, {"__id__": 7}, {"__id__": 9}, {"__id__": 13}, {"__id__": 17}, {"__id__": 21}, {"__id__": 25}, {"__id__": 29}, {"__id__": 33}, {"__id__": 37}, {"__id__": 45}, {"__id__": 71}, {"__id__": 99}, {"__id__": 164}], "_active": true, "_components": [{"__id__": 414}, {"__id__": 415}, {"__id__": 416}, {"__id__": 417}], "_prefab": null, "_opacity": 0, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 2001, "height": 1125}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [1000.5, 562.5, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "a5esZu+45LA5mBpvttspPD"}, {"__type__": "cc.Node", "_name": "Main Camera", "_objFlags": 0, "_parent": {"__id__": 4}, "_children": [], "_active": true, "_components": [{"__id__": 6}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 2001, "height": 1125}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 289.7504600427777, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "e1WoFrQ79G7r4ZuQE3HlNb"}, {"__type__": "cc.Camera", "_name": "", "_objFlags": 0, "node": {"__id__": 5}, "_enabled": true, "_cullingMask": -1, "_clearFlags": 7, "_backgroundColor": {"__type__": "cc.Color", "r": 249, "g": 209, "b": 162, "a": 255}, "_depth": -1, "_zoomRatio": 1, "_targetTexture": null, "_fov": 60, "_orthoSize": 10, "_nearClip": 1, "_farClip": 4096, "_ortho": true, "_rect": {"__type__": "cc.Rect", "x": 0, "y": 0, "width": 1, "height": 1}, "_renderStages": 1, "_alignWithScreen": true, "_id": "81GN3uXINKVLeW4+iKSlim"}, {"__type__": "cc.Node", "_name": "indexButtonFun", "_objFlags": 0, "_parent": {"__id__": 4}, "_children": [], "_active": true, "_components": [{"__id__": 8}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "39dMQMV0lMcJWS7wIAxgU5"}, {"__type__": "2a73fwk0tJE3JoWlQbfxDQI", "_name": "", "_objFlags": 0, "node": {"__id__": 7}, "_enabled": true, "_id": "15zId9dohB7b++2ugrqt4U"}, {"__type__": "cc.Node", "_name": "sky", "_objFlags": 0, "_parent": {"__id__": 4}, "_children": [], "_active": true, "_components": [{"__id__": 10}, {"__id__": 11}, {"__id__": 12}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 2001, "height": 1125}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "b4NsGjhutCvrSSLwW+UZkG"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 9}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": null, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "cd8iPIqRpDCbiGT3OFwHn0"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 9}, "_enabled": true, "alignMode": 2, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 2001, "_originalHeight": 1125, "_id": "4c2VDgaTFKApzm7myZkP0F"}, {"__type__": "e0139aGBBFIBLYUi7ZvSe2q", "_name": "", "_objFlags": 0, "node": {"__id__": 9}, "_enabled": true, "_id": "e2Kepl/atK6rim7Xf8pxaF"}, {"__type__": "cc.Node", "_name": "cloud", "_objFlags": 0, "_parent": {"__id__": 4}, "_children": [], "_active": true, "_components": [{"__id__": 14}, {"__id__": 15}, {"__id__": 16}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 2001, "height": 946}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -562.5, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "28IZMh7RtCq7UmQqJvJ9bL"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 13}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": null, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "60oux5vURAcLy6JYP3ASKH"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 13}, "_enabled": true, "alignMode": 2, "_target": null, "_alignFlags": 44, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 2001, "_originalHeight": 0, "_id": "8bGdME8o1Fe7qDlweOv8ZY"}, {"__type__": "e0139aGBBFIBLYUi7ZvSe2q", "_name": "", "_objFlags": 0, "node": {"__id__": 13}, "_enabled": true, "_id": "a9sas+oGJMs7PSP5UQdpWz"}, {"__type__": "cc.Node", "_name": "grass", "_objFlags": 0, "_parent": {"__id__": 4}, "_children": [], "_active": true, "_components": [{"__id__": 18}, {"__id__": 19}, {"__id__": 20}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 2001, "height": 327}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -562.5, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "563uTY/AxIR6UMuVPU2oOU"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 17}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": null, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "d59meRKdBKK7pX4FWLzf3x"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 17}, "_enabled": true, "alignMode": 2, "_target": null, "_alignFlags": 44, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 2001, "_originalHeight": 0, "_id": "46/Y+xO8xORLepeYLGXKn+"}, {"__type__": "e0139aGBBFIBLYUi7ZvSe2q", "_name": "", "_objFlags": 0, "node": {"__id__": 17}, "_enabled": true, "_id": "042A1DXmxGcbd3mMN2WhfS"}, {"__type__": "cc.Node", "_name": "<PERSON><PERSON><PERSON>", "_objFlags": 0, "_parent": {"__id__": 4}, "_children": [], "_active": true, "_components": [{"__id__": 22}, {"__id__": 23}, {"__id__": 24}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 633, "height": 596}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 1, "y": 0}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [1000.5, -562.5, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "23aZ0DxbxGILmH7zxIrQNa"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 21}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": null, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "b3hY/L7GZE17FhRo5ZMmWV"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 21}, "_enabled": true, "alignMode": 2, "_target": null, "_alignFlags": 36, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": "03rnRYvtZIHobW9cBYpUG8"}, {"__type__": "e0139aGBBFIBLYUi7ZvSe2q", "_name": "", "_objFlags": 0, "node": {"__id__": 21}, "_enabled": true, "_id": "ea6Pltl1NLwrvJqV7IjOEe"}, {"__type__": "cc.Node", "_name": "leftJelly", "_objFlags": 0, "_parent": {"__id__": 4}, "_children": [], "_active": true, "_components": [{"__id__": 26}, {"__id__": 27}, {"__id__": 28}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 795, "height": 897}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-1000.5, -562.5, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "92A2vX4j1IrZZq14n4qYqC"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 25}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": null, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "bb8s0dQw9HSqWmTafzxDH6"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 25}, "_enabled": true, "alignMode": 2, "_target": null, "_alignFlags": 12, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": "5bckJ465dGH4/qyuPPnQCT"}, {"__type__": "e0139aGBBFIBLYUi7ZvSe2q", "_name": "", "_objFlags": 0, "node": {"__id__": 25}, "_enabled": true, "_id": "cdOkmV6klOmr39TC13aT+4"}, {"__type__": "cc.Node", "_name": "rightBalloon", "_objFlags": 0, "_parent": {"__id__": 4}, "_children": [], "_active": true, "_components": [{"__id__": 30}, {"__id__": 31}, {"__id__": 32}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 354, "height": 510}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [665.903, 134.074, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "37gv4x83pLwal2kddtoyNn"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 29}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": null, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": null, "_id": "e1QJRTEspNOIp5i12tyShO"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 29}, "_enabled": true, "alignMode": 2, "_target": null, "_alignFlags": 36, "_left": 0, "_right": 157.59699999999998, "_top": 0, "_bottom": 441.574, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": "59SYPVkjNDeoCmdKpdtNUQ"}, {"__type__": "e0139aGBBFIBLYUi7ZvSe2q", "_name": "", "_objFlags": 0, "node": {"__id__": 29}, "_enabled": true, "_id": "90PGii0adIO63SeMVEQQoW"}, {"__type__": "cc.Node", "_name": "leftBalloon", "_objFlags": 0, "_parent": {"__id__": 4}, "_children": [], "_active": true, "_components": [{"__id__": 34}, {"__id__": 35}, {"__id__": 36}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 242, "height": 192}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-701.657, 174.29700000000003, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "a9m7kAT/JNf4z6FzK+HTsn"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 33}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": null, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "a0+UT2ijBHE5KfJaytoZtO"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 33}, "_enabled": true, "alignMode": 2, "_target": null, "_alignFlags": 12, "_left": 177.84299999999996, "_right": 0, "_top": 0, "_bottom": 640.797, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": "d2SHn2rS9LVbexFQNpBgce"}, {"__type__": "e0139aGBBFIBLYUi7ZvSe2q", "_name": "", "_objFlags": 0, "node": {"__id__": 33}, "_enabled": true, "_id": "f67qO6AbZBl4BiQEk79wDc"}, {"__type__": "cc.Node", "_name": "title", "_objFlags": 0, "_parent": {"__id__": 4}, "_children": [{"__id__": 38}], "_active": true, "_components": [{"__id__": 42}, {"__id__": 43}, {"__id__": 44}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 455, "height": 348}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 151.951, 0, 0, 0, 0, 1, 1.4, 1.4, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "b29javE0xLebmN2PSsAy1N"}, {"__type__": "cc.Node", "_name": "<PERSON><PERSON><PERSON>", "_objFlags": 0, "_parent": {"__id__": 37}, "_children": [], "_active": true, "_components": [{"__id__": 39}, {"__id__": 40}, {"__id__": 41}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 442, "height": 343}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-131.445, -97.158, 0, 0, 0, 0, 1, 0.35, 0.35, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "f6nVSlkHlJzoSpvjkr8qbI"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 38}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": null, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "b7Pc+T+OFK65Nht+LI8xZ0"}, {"__type__": "e0139aGBBFIBLYUi7ZvSe2q", "_name": "", "_objFlags": 0, "node": {"__id__": 38}, "_enabled": true, "_id": "6a9kJOnOFIdrIrfrSZ4YEm"}, {"__type__": "cc.Animation", "_name": "", "_objFlags": 0, "node": {"__id__": 38}, "_enabled": true, "_defaultClip": {"__uuid__": "b7b38943-53cc-42e0-af91-66be3aa90c53"}, "_clips": [{"__uuid__": "b7b38943-53cc-42e0-af91-66be3aa90c53"}], "playOnLoad": true, "_id": "85Q73Mx6FLv6VR4w85nHEg"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 37}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": null, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "2eKFNtUu5EDJH5fFwPNr0C"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 37}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 16, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": "68/C2i/0RNQ6naXNZRY9a9"}, {"__type__": "e0139aGBBFIBLYUi7ZvSe2q", "_name": "", "_objFlags": 0, "node": {"__id__": 37}, "_enabled": true, "_id": "62Sswr/qVByqYi3t1aePxw"}, {"__type__": "cc.Node", "_name": "ui", "_objFlags": 0, "_parent": {"__id__": 4}, "_children": [{"__id__": 46}, {"__id__": 54}, {"__id__": 62}], "_active": true, "_components": [{"__id__": 70}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -262.5, 0, 0, 0, 0, 1, 2, 2, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 4, "groupIndex": 4, "_id": "a5NOGOx9tHHKU194AdP1gF"}, {"__type__": "cc.Node", "_name": "topButton", "_objFlags": 0, "_parent": {"__id__": 45}, "_children": [{"__id__": 47}], "_active": true, "_components": [{"__id__": 51}, {"__id__": 53}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [140, -20, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 4, "groupIndex": 4, "_id": "1e3KAfFE1K6avYUWDRJoYk"}, {"__type__": "cc.Node", "_name": "topButtonBg", "_objFlags": 512, "_parent": {"__id__": 46}, "_children": [], "_active": true, "_components": [{"__id__": 48}, {"__id__": 49}, {"__id__": 50}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 115, "height": 114}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 0.9, 0.9, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 4, "groupIndex": 4, "_id": "7flF8AWy5AjqHKsgYdf9JU"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 47}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "8cdb44ac-a3f6-449f-b354-7cd48cf84061"}, "_type": 1, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "ccEASnayhLBLx59R1+uMo6"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 47}, "_enabled": false, "alignMode": 0, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 100, "_originalHeight": 40, "_id": "e0vng6FHRHjJ4h+X73hzDj"}, {"__type__": "e0139aGBBFIBLYUi7ZvSe2q", "_name": "", "_objFlags": 0, "node": {"__id__": 47}, "_enabled": true, "_id": "48Ld7ACUZAep66exq4pebR"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 46}, "_enabled": true, "_normalMaterial": {"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}, "_grayMaterial": null, "duration": 0.1, "zoomScale": 0.9, "clickEvents": [{"__id__": 52}], "_N$interactable": true, "_N$enableAutoGrayEffect": false, "_N$transition": 3, "transition": 3, "_N$normalColor": {"__type__": "cc.Color", "r": 230, "g": 230, "b": 230, "a": 255}, "_N$pressedColor": {"__type__": "cc.Color", "r": 200, "g": 200, "b": 200, "a": 255}, "pressedColor": {"__type__": "cc.Color", "r": 200, "g": 200, "b": 200, "a": 255}, "_N$hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$disabledColor": {"__type__": "cc.Color", "r": 120, "g": 120, "b": 120, "a": 200}, "_N$normalSprite": null, "_N$pressedSprite": null, "pressedSprite": null, "_N$hoverSprite": null, "hoverSprite": null, "_N$disabledSprite": null, "_N$target": {"__id__": 47}, "_id": "cakYCDrbJNxZngbcLxhGFp"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 7}, "component": "", "_componentId": "2a73fwk0tJE3JoWlQbfxDQI", "handler": "topButton", "customEventData": ""}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 46}, "_enabled": true, "alignMode": 2, "_target": null, "_alignFlags": 18, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": -20, "_horizontalCenter": 140, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": "5dYMbzGm1M0IOG9R6q1MV4"}, {"__type__": "cc.Node", "_name": "playButton", "_objFlags": 0, "_parent": {"__id__": 45}, "_children": [{"__id__": 55}], "_active": true, "_components": [{"__id__": 59}, {"__id__": 61}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 120, "height": 120}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 4, "groupIndex": 4, "_id": "f0Cl2RwGhFCZy6WawhiGL4"}, {"__type__": "cc.Node", "_name": "playButtonBg", "_objFlags": 512, "_parent": {"__id__": 54}, "_children": [], "_active": true, "_components": [{"__id__": 56}, {"__id__": 57}, {"__id__": 58}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 116, "height": 116}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1.09, 1.09, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 4, "groupIndex": 4, "_id": "91ciQniK9Mp5QH1bMv5uTs"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 55}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "8cdb44ac-a3f6-449f-b354-7cd48cf84061"}, "_type": 1, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "bdvrfj+ZtJw7ML5OxCSbWS"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 55}, "_enabled": false, "alignMode": 0, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 100, "_originalHeight": 40, "_id": "dd06qU7BtCaqwc7511/Wu1"}, {"__type__": "e0139aGBBFIBLYUi7ZvSe2q", "_name": "", "_objFlags": 0, "node": {"__id__": 55}, "_enabled": true, "_id": "7fTy+JmvFNUaC3vL6DRW4e"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 54}, "_enabled": true, "_normalMaterial": {"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}, "_grayMaterial": null, "duration": 0.1, "zoomScale": 0.9, "clickEvents": [{"__id__": 60}], "_N$interactable": true, "_N$enableAutoGrayEffect": false, "_N$transition": 3, "transition": 3, "_N$normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$pressedColor": {"__type__": "cc.Color", "r": 200, "g": 200, "b": 200, "a": 255}, "pressedColor": {"__type__": "cc.Color", "r": 200, "g": 200, "b": 200, "a": 255}, "_N$hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$disabledColor": {"__type__": "cc.Color", "r": 120, "g": 120, "b": 120, "a": 200}, "_N$normalSprite": null, "_N$pressedSprite": null, "pressedSprite": null, "_N$hoverSprite": null, "hoverSprite": null, "_N$disabledSprite": null, "_N$target": {"__id__": 55}, "_id": "2dy1PLaERMZb1kGHRlo5qp"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 7}, "component": "", "_componentId": "2a73fwk0tJE3JoWlQbfxDQI", "handler": "playButton", "customEventData": ""}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 54}, "_enabled": true, "alignMode": 2, "_target": null, "_alignFlags": 18, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": "8feV95+EJD3LYpfdwY8xx/"}, {"__type__": "cc.Node", "_name": "skinButton", "_objFlags": 0, "_parent": {"__id__": 45}, "_children": [{"__id__": 63}], "_active": true, "_components": [{"__id__": 67}, {"__id__": 69}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-140, -20, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 4, "groupIndex": 4, "_id": "14BaBjQ9VCCpQLIsyma3W9"}, {"__type__": "cc.Node", "_name": "skinButtonBg", "_objFlags": 512, "_parent": {"__id__": 62}, "_children": [], "_active": true, "_components": [{"__id__": 64}, {"__id__": 65}, {"__id__": 66}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 116, "height": 115}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 0.9, 0.9, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 4, "groupIndex": 4, "_id": "d017zV9bVHyIJJvlv/3xze"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 63}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "8cdb44ac-a3f6-449f-b354-7cd48cf84061"}, "_type": 1, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "e0OCFDmohG/IWo+HkpNaMi"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 63}, "_enabled": false, "alignMode": 0, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 100, "_originalHeight": 40, "_id": "0aDhLbLA9C05rziquXamHs"}, {"__type__": "e0139aGBBFIBLYUi7ZvSe2q", "_name": "", "_objFlags": 0, "node": {"__id__": 63}, "_enabled": true, "_id": "32ioFX079FRKnppjO9KswT"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 62}, "_enabled": true, "_normalMaterial": {"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}, "_grayMaterial": null, "duration": 0.1, "zoomScale": 0.9, "clickEvents": [{"__id__": 68}], "_N$interactable": true, "_N$enableAutoGrayEffect": false, "_N$transition": 3, "transition": 3, "_N$normalColor": {"__type__": "cc.Color", "r": 230, "g": 230, "b": 230, "a": 255}, "_N$pressedColor": {"__type__": "cc.Color", "r": 200, "g": 200, "b": 200, "a": 255}, "pressedColor": {"__type__": "cc.Color", "r": 200, "g": 200, "b": 200, "a": 255}, "_N$hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$disabledColor": {"__type__": "cc.Color", "r": 120, "g": 120, "b": 120, "a": 200}, "_N$normalSprite": null, "_N$pressedSprite": null, "pressedSprite": null, "_N$hoverSprite": null, "hoverSprite": null, "_N$disabledSprite": null, "_N$target": {"__id__": 63}, "_id": "c29LC0RGdOUZQfAxtFsZNH"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 7}, "component": "", "_componentId": "2a73fwk0tJE3JoWlQbfxDQI", "handler": "skinButton", "customEventData": ""}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 62}, "_enabled": true, "alignMode": 2, "_target": null, "_alignFlags": 18, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": -20, "_horizontalCenter": -140, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": "1bzkLtgUdLWbTKewOyL+fg"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 45}, "_enabled": true, "alignMode": 2, "_target": null, "_alignFlags": 20, "_left": 0, "_right": 0, "_top": 0, "_bottom": 300, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": "c0jKHxlgFOjaD5dsLCkAK5"}, {"__type__": "cc.Node", "_name": "more<PERSON><PERSON><PERSON>", "_objFlags": 0, "_parent": {"__id__": 4}, "_children": [{"__id__": 72}, {"__id__": 75}, {"__id__": 82}, {"__id__": 89}], "_active": true, "_components": [{"__id__": 96}, {"__id__": 97}, {"__id__": 98}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-880.5, 442.5, 0, 0, 0, 0, 1, 1.2, 1.2, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "67oS+Zz4RO34D157X4hF2/"}, {"__type__": "cc.Node", "_name": "moreBg", "_objFlags": 0, "_parent": {"__id__": 71}, "_children": [], "_active": true, "_components": [{"__id__": 73}, {"__id__": 74}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 107, "height": 107}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-53.5, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "2bn2BXvWBA65NmSfeKJ6HJ"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 72}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": null, "_type": 1, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "13H9tzxF9OLruNyRRwkqL6"}, {"__type__": "e0139aGBBFIBLYUi7ZvSe2q", "_name": "", "_objFlags": 0, "node": {"__id__": 72}, "_enabled": true, "_id": "69hn9tCiVE5JN2KLE9GNXk"}, {"__type__": "cc.Node", "_name": "helpButton", "_objFlags": 0, "_parent": {"__id__": 71}, "_children": [{"__id__": 76}], "_active": true, "_components": [{"__id__": 80}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 0.7, 0.7, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "5aKprQtvBNWK2ziW0HkX8P"}, {"__type__": "cc.Node", "_name": "helpButtonBg", "_objFlags": 512, "_parent": {"__id__": 75}, "_children": [], "_active": true, "_components": [{"__id__": 77}, {"__id__": 78}, {"__id__": 79}], "_prefab": null, "_opacity": 200, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 102, "height": 102}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -1.2999999999999972, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "fdShJ6YRJBEahnte9rRjBC"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 76}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "8cdb44ac-a3f6-449f-b354-7cd48cf84061"}, "_type": 1, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "90vJz0mlBLTJWdkexY+85A"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 76}, "_enabled": true, "alignMode": 0, "_target": null, "_alignFlags": 45, "_left": -1, "_right": -1, "_top": 0.2999999999999998, "_bottom": -2.299999999999999, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 100, "_originalHeight": 40, "_id": "2fZCNHbghBtpgVyUMyNduo"}, {"__type__": "e0139aGBBFIBLYUi7ZvSe2q", "_name": "", "_objFlags": 0, "node": {"__id__": 76}, "_enabled": true, "_id": "94Nt5NRtZKYb/Rn++Fazx1"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 75}, "_enabled": true, "_normalMaterial": {"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}, "_grayMaterial": null, "duration": 0.1, "zoomScale": 0.9, "clickEvents": [{"__id__": 81}], "_N$interactable": true, "_N$enableAutoGrayEffect": false, "_N$transition": 3, "transition": 3, "_N$normalColor": {"__type__": "cc.Color", "r": 230, "g": 230, "b": 230, "a": 255}, "_N$pressedColor": {"__type__": "cc.Color", "r": 200, "g": 200, "b": 200, "a": 255}, "pressedColor": {"__type__": "cc.Color", "r": 200, "g": 200, "b": 200, "a": 255}, "_N$hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$disabledColor": {"__type__": "cc.Color", "r": 120, "g": 120, "b": 120, "a": 200}, "_N$normalSprite": null, "_N$pressedSprite": null, "pressedSprite": null, "_N$hoverSprite": null, "hoverSprite": null, "_N$disabledSprite": null, "_N$target": {"__id__": 76}, "_id": "18Tz0f/B1IGYxKxePOnbJM"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 71}, "component": "", "_componentId": "235dfPanl5Bo7YIOqwVsb3e", "handler": "helpButton", "customEventData": ""}, {"__type__": "cc.Node", "_name": "setButton", "_objFlags": 0, "_parent": {"__id__": 71}, "_children": [{"__id__": 83}], "_active": true, "_components": [{"__id__": 87}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 0.7, 0.7, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "b9SOvCIg1MgI6nGaWOhyfg"}, {"__type__": "cc.Node", "_name": "setButtonBg", "_objFlags": 512, "_parent": {"__id__": 82}, "_children": [], "_active": true, "_components": [{"__id__": 84}, {"__id__": 85}, {"__id__": 86}], "_prefab": null, "_opacity": 200, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 102, "height": 106}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 2.6000000000000014, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "7fP829q0NHELaqiyqpjuZD"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 83}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "8cdb44ac-a3f6-449f-b354-7cd48cf84061"}, "_type": 1, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "80dmBhmiRHxbHzH/eNqzJS"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 83}, "_enabled": true, "alignMode": 0, "_target": null, "_alignFlags": 45, "_left": -1, "_right": -1, "_top": -5.6, "_bottom": -0.40000000000000036, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 100, "_originalHeight": 40, "_id": "36mB9tX0VApLILRNrKwXjf"}, {"__type__": "e0139aGBBFIBLYUi7ZvSe2q", "_name": "", "_objFlags": 0, "node": {"__id__": 83}, "_enabled": true, "_id": "58dxm8I/xJXq6f6RT0Nmgk"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 82}, "_enabled": true, "_normalMaterial": {"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}, "_grayMaterial": null, "duration": 0.1, "zoomScale": 0.9, "clickEvents": [{"__id__": 88}], "_N$interactable": true, "_N$enableAutoGrayEffect": false, "_N$transition": 3, "transition": 3, "_N$normalColor": {"__type__": "cc.Color", "r": 230, "g": 230, "b": 230, "a": 255}, "_N$pressedColor": {"__type__": "cc.Color", "r": 200, "g": 200, "b": 200, "a": 255}, "pressedColor": {"__type__": "cc.Color", "r": 200, "g": 200, "b": 200, "a": 255}, "_N$hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$disabledColor": {"__type__": "cc.Color", "r": 120, "g": 120, "b": 120, "a": 200}, "_N$normalSprite": null, "_N$pressedSprite": null, "pressedSprite": null, "_N$hoverSprite": null, "hoverSprite": null, "_N$disabledSprite": null, "_N$target": {"__id__": 83}, "_id": "952FZ16rlHMKPyWYr9WzUq"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 71}, "component": "", "_componentId": "235dfPanl5Bo7YIOqwVsb3e", "handler": "setButton", "customEventData": ""}, {"__type__": "cc.Node", "_name": "moreButton", "_objFlags": 0, "_parent": {"__id__": 71}, "_children": [{"__id__": 90}], "_active": true, "_components": [{"__id__": 94}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 115, "height": 115}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 4, "groupIndex": 4, "_id": "995tneAL9Oj6dR4Fvj/Yza"}, {"__type__": "cc.Node", "_name": "moreButtonBg", "_objFlags": 512, "_parent": {"__id__": 89}, "_children": [], "_active": true, "_components": [{"__id__": 91}, {"__id__": 92}, {"__id__": 93}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 115, "height": 116}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 0]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 4, "groupIndex": 4, "_id": "4aarntm+pPKL1pVUkmZzPj"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 90}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "8cdb44ac-a3f6-449f-b354-7cd48cf84061"}, "_type": 1, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "c6bS60GpxHw4iMYN/OXVnI"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 90}, "_enabled": true, "alignMode": 0, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 0, "_top": -0.5, "_bottom": -0.5, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 100, "_originalHeight": 40, "_id": "22IMyoHeVEwoW29vbJh/aJ"}, {"__type__": "e0139aGBBFIBLYUi7ZvSe2q", "_name": "", "_objFlags": 0, "node": {"__id__": 90}, "_enabled": true, "_id": "b0U7z8VyhIOZrN/PDHOGYY"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 89}, "_enabled": true, "_normalMaterial": {"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}, "_grayMaterial": null, "duration": 0.1, "zoomScale": 1.2, "clickEvents": [{"__id__": 95}], "_N$interactable": true, "_N$enableAutoGrayEffect": false, "_N$transition": 1, "transition": 1, "_N$normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$pressedColor": {"__type__": "cc.Color", "r": 223, "g": 223, "b": 223, "a": 255}, "pressedColor": {"__type__": "cc.Color", "r": 223, "g": 223, "b": 223, "a": 255}, "_N$hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$disabledColor": {"__type__": "cc.Color", "r": 120, "g": 120, "b": 120, "a": 200}, "_N$normalSprite": null, "_N$pressedSprite": null, "pressedSprite": null, "_N$hoverSprite": null, "hoverSprite": null, "_N$disabledSprite": null, "_N$target": {"__id__": 90}, "_id": "41hHSJUdJC+4js4IeU9UHP"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 71}, "component": "", "_componentId": "235dfPanl5Bo7YIOqwVsb3e", "handler": "moreButton", "customEventData": "2"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 71}, "_enabled": true, "alignMode": 2, "_target": null, "_alignFlags": 9, "_left": 60, "_right": 0, "_top": 60, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": "dfW0KjlcxBdbmjVM5N+rDU"}, {"__type__": "cc.Animation", "_name": "", "_objFlags": 0, "node": {"__id__": 71}, "_enabled": true, "_defaultClip": null, "_clips": [{"__uuid__": "2fa8d40b-beea-4d8e-b0ba-79e2c4360259"}, {"__uuid__": "4850fa21-228c-4475-a36d-6781fab2e45f"}], "playOnLoad": false, "_id": "b2gVhEjXlHL5sleWBeulu+"}, {"__type__": "235dfPanl5Bo7YIOqwVsb3e", "_name": "", "_objFlags": 0, "node": {"__id__": 71}, "_enabled": true, "anim": {"__id__": 97}, "setWin": {"__id__": 99}, "helpWin": {"__id__": 164}, "continueWin": null, "textWin": null, "gameCanvas": null, "_id": "98fU37PvRIWbjlksZXGn9Q"}, {"__type__": "cc.Node", "_name": "setWin", "_objFlags": 0, "_parent": {"__id__": 4}, "_children": [{"__id__": 100}, {"__id__": 104}], "_active": false, "_components": [{"__id__": 163}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 2001, "height": 1125}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "e09YPocltLA6IJD24/bjNm"}, {"__type__": "cc.Node", "_name": "grayBg", "_objFlags": 0, "_parent": {"__id__": 99}, "_children": [], "_active": true, "_components": [{"__id__": 101}, {"__id__": 102}, {"__id__": 103}], "_prefab": null, "_opacity": 0, "_color": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 2001, "height": 1125}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "40ePRA7WZM8IdgtspPuRfM"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 100}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "a23235d1-15db-4b95-8439-a2e005bfff91"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "c975a770JDAbOvYErUordT"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 100}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 100, "_originalHeight": 100, "_id": "a6fvRm57tBMp1ztgumz0U3"}, {"__type__": "cc.BlockInputEvents", "_name": "", "_objFlags": 0, "node": {"__id__": 100}, "_enabled": true, "_id": "e3P7yOmRdDKKKSQyCB245Y"}, {"__type__": "cc.Node", "_name": "setWinBg", "_objFlags": 0, "_parent": {"__id__": 99}, "_children": [{"__id__": 105}, {"__id__": 152}], "_active": true, "_components": [{"__id__": 160}, {"__id__": 161}, {"__id__": 162}], "_prefab": null, "_opacity": 0, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 510, "height": 260}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "bfC1VcngZG14BnRI7aPKLL"}, {"__type__": "cc.Node", "_name": "content", "_objFlags": 0, "_parent": {"__id__": 104}, "_children": [{"__id__": 106}, {"__id__": 129}], "_active": true, "_components": [], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "b1qWsQYVhNVYW8MzGhg0GE"}, {"__type__": "cc.Node", "_name": "effectVol", "_objFlags": 0, "_parent": {"__id__": 105}, "_children": [{"__id__": 107}, {"__id__": 123}], "_active": true, "_components": [{"__id__": 128}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 340, "height": 40}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 26, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "8daCZ5U2lGWqBb6A8ZfLAl"}, {"__type__": "cc.Node", "_name": "slider", "_objFlags": 0, "_parent": {"__id__": 106}, "_children": [{"__id__": 108}, {"__id__": 111}, {"__id__": 114}], "_active": true, "_components": [{"__id__": 119}, {"__id__": 121}, {"__id__": 122}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 240, "height": 41}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [50, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "82p8Lhd4VNppXDleGJGyDb"}, {"__type__": "cc.Node", "_name": "sliderBg", "_objFlags": 512, "_parent": {"__id__": 107}, "_children": [], "_active": true, "_components": [{"__id__": 109}, {"__id__": 110}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 280, "height": 41}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "cdy152GqBNJ75tfB+lt2Nw"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 108}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "8cdb44ac-a3f6-449f-b354-7cd48cf84061"}, "_type": 1, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "6bjs8G2S5Ad7Ktv/3s11uN"}, {"__type__": "e0139aGBBFIBLYUi7ZvSe2q", "_name": "", "_objFlags": 0, "node": {"__id__": 108}, "_enabled": true, "_id": "095i9y/LhOyZ1KEFNng9ru"}, {"__type__": "cc.Node", "_name": "slider<PERSON><PERSON>", "_objFlags": 512, "_parent": {"__id__": 107}, "_children": [], "_active": true, "_components": [{"__id__": 112}, {"__id__": 113}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 270, "height": 30}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-135, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "018srtbjpNx7XMs2mjnaA6"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 111}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "8cdb44ac-a3f6-449f-b354-7cd48cf84061"}, "_type": 1, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "9aUfc0wuFM+75wlSRldGvT"}, {"__type__": "e0139aGBBFIBLYUi7ZvSe2q", "_name": "", "_objFlags": 0, "node": {"__id__": 111}, "_enabled": true, "_id": "38RZdIPbZC7rxldZv6Zp5L"}, {"__type__": "cc.Node", "_name": "slide<PERSON><PERSON><PERSON><PERSON>", "_objFlags": 512, "_parent": {"__id__": 107}, "_children": [], "_active": true, "_components": [{"__id__": 115}, {"__id__": 116}, {"__id__": 118}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 30, "height": 41}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [120, 5.684341886080802e-14, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "53/cAEwblPfrwzYB4IHqA7"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 114}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "8cdb44ac-a3f6-449f-b354-7cd48cf84061"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "36lE7ZwgJDvbEv6fxeSPMA"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 114}, "_enabled": true, "_normalMaterial": null, "_grayMaterial": null, "duration": 0.1, "zoomScale": 1.2, "clickEvents": [{"__id__": 117}], "_N$interactable": true, "_N$enableAutoGrayEffect": true, "_N$transition": 3, "transition": 3, "_N$normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$disabledColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$normalSprite": {"__uuid__": "e7aba14b-f956-4480-b254-8d57832e273f"}, "_N$pressedSprite": {"__uuid__": "e7aba14b-f956-4480-b254-8d57832e273f"}, "pressedSprite": {"__uuid__": "e7aba14b-f956-4480-b254-8d57832e273f"}, "_N$hoverSprite": {"__uuid__": "e7aba14b-f956-4480-b254-8d57832e273f"}, "hoverSprite": {"__uuid__": "e7aba14b-f956-4480-b254-8d57832e273f"}, "_N$disabledSprite": {"__uuid__": "29158224-f8dd-4661-a796-1ffab537140e"}, "_N$target": {"__id__": 114}, "_id": "8a9LTiIUJIlIwJks7XPuND"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 71}, "component": "", "_componentId": "235dfPanl5Bo7YIOqwVsb3e", "handler": "slide<PERSON><PERSON><PERSON><PERSON>", "customEventData": ""}, {"__type__": "e0139aGBBFIBLYUi7ZvSe2q", "_name": "", "_objFlags": 0, "node": {"__id__": 114}, "_enabled": true, "_id": "c4Ze8aLWtFl5eaPR6+tdws"}, {"__type__": "cc.Slider", "_name": "", "_objFlags": 0, "node": {"__id__": 107}, "_enabled": true, "direction": 0, "slideEvents": [{"__id__": 120}], "_N$handle": {"__id__": 116}, "_N$progress": 1, "_id": "19SYlArglGoIf9VP1c8/AL"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 107}, "component": "", "_componentId": "d720bc3df5BvI5vLHVhuwS9", "handler": "slideEvent", "customEventData": ""}, {"__type__": "d720bc3df5BvI5vLHVhuwS9", "_name": "", "_objFlags": 0, "node": {"__id__": 107}, "_enabled": true, "sliderBar": {"__id__": 111}, "sliderHandle": {"__id__": 114}, "_id": "2b+rTJlltPuKGlfJ//dy3W"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 107}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 34, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": "6bL/mOWZxKyaOL3bG3ism+"}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 0, "_parent": {"__id__": 106}, "_children": [], "_active": true, "_components": [{"__id__": 124}, {"__id__": 125}, {"__id__": 126}, {"__id__": 127}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 90, "height": 37.8}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-128, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "dbtsqZ6jBBB5LMeejITM9g"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 123}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_useOriginalSize": false, "_string": "音效：", "_N$string": "音效：", "_fontSize": 30, "_lineHeight": 30, "_enableWrapText": true, "_N$file": null, "_isSystemFontUsed": true, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 1, "_N$verticalAlign": 0, "_N$fontFamily": "华文琥珀", "_N$overflow": 0, "_N$cacheMode": 0, "_id": "5fssjNGL9DUKkuEePekO2M"}, {"__type__": "cc.LabelShadow", "_name": "", "_objFlags": 0, "node": {"__id__": 123}, "_enabled": true, "_color": {"__type__": "cc.Color", "r": 59, "g": 59, "b": 59, "a": 255}, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_blur": 4, "_id": "112AIYIpNNLrVnyHbAh7Gf"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 123}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 10, "_left": -3, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": "0a+xLXL/9PibUQAlZfkwMS"}, {"__type__": "5520aFIq0hD17P+ce6P30KM", "_name": "", "_objFlags": 0, "node": {"__id__": 123}, "_enabled": true, "num": 0, "_id": "dbx1uzTc9PvK/UFYbipEnt"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 106}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 2, "_left": 255, "_right": 255, "_top": 0, "_bottom": 0, "_verticalCenter": 26, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": "72iECQXY5FjLTSDv5CuNUg"}, {"__type__": "cc.Node", "_name": "bgmVol", "_objFlags": 0, "_parent": {"__id__": 105}, "_children": [{"__id__": 130}, {"__id__": 146}], "_active": true, "_components": [{"__id__": 151}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 340, "height": 40}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -26, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "53CDMYmKtP8J9WK7zjewHx"}, {"__type__": "cc.Node", "_name": "slider", "_objFlags": 0, "_parent": {"__id__": 129}, "_children": [{"__id__": 131}, {"__id__": 134}, {"__id__": 137}], "_active": true, "_components": [{"__id__": 142}, {"__id__": 144}, {"__id__": 145}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 240, "height": 41}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [50, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "2caP0IkK9Dd7gAtsWnn3s8"}, {"__type__": "cc.Node", "_name": "sliderBg", "_objFlags": 512, "_parent": {"__id__": 130}, "_children": [], "_active": true, "_components": [{"__id__": 132}, {"__id__": 133}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 280, "height": 41}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "7e1PAOFAxHPKzt1EFqHBBo"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 131}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "8cdb44ac-a3f6-449f-b354-7cd48cf84061"}, "_type": 1, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "0a1bY6S09M6oeJ7mlCGErG"}, {"__type__": "e0139aGBBFIBLYUi7ZvSe2q", "_name": "", "_objFlags": 0, "node": {"__id__": 131}, "_enabled": true, "_id": "d82XGjzQZF/5UTt4QoMArF"}, {"__type__": "cc.Node", "_name": "slider<PERSON><PERSON>", "_objFlags": 512, "_parent": {"__id__": 130}, "_children": [], "_active": true, "_components": [{"__id__": 135}, {"__id__": 136}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 270, "height": 30}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-135, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "24nyRqTvpEH5FYeqGgtm4P"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 134}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "8cdb44ac-a3f6-449f-b354-7cd48cf84061"}, "_type": 1, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "e9zBiHKkJItbU9fVYDeOcF"}, {"__type__": "e0139aGBBFIBLYUi7ZvSe2q", "_name": "", "_objFlags": 0, "node": {"__id__": 134}, "_enabled": true, "_id": "fcxOOSz3hOQr3I6oRw3EtB"}, {"__type__": "cc.Node", "_name": "slide<PERSON><PERSON><PERSON><PERSON>", "_objFlags": 512, "_parent": {"__id__": 130}, "_children": [], "_active": true, "_components": [{"__id__": 138}, {"__id__": 139}, {"__id__": 141}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 30, "height": 41}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [120, 5.684341886080802e-14, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "53/xr0KUBAsqzq+mD+xMy+"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 137}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "8cdb44ac-a3f6-449f-b354-7cd48cf84061"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "f45fJ4pKFJ7KiKAD7sAAY9"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 137}, "_enabled": true, "_normalMaterial": null, "_grayMaterial": null, "duration": 0.1, "zoomScale": 1.2, "clickEvents": [{"__id__": 140}], "_N$interactable": true, "_N$enableAutoGrayEffect": true, "_N$transition": 3, "transition": 3, "_N$normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$disabledColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$normalSprite": {"__uuid__": "e7aba14b-f956-4480-b254-8d57832e273f"}, "_N$pressedSprite": {"__uuid__": "e7aba14b-f956-4480-b254-8d57832e273f"}, "pressedSprite": {"__uuid__": "e7aba14b-f956-4480-b254-8d57832e273f"}, "_N$hoverSprite": {"__uuid__": "e7aba14b-f956-4480-b254-8d57832e273f"}, "hoverSprite": {"__uuid__": "e7aba14b-f956-4480-b254-8d57832e273f"}, "_N$disabledSprite": {"__uuid__": "29158224-f8dd-4661-a796-1ffab537140e"}, "_N$target": {"__id__": 137}, "_id": "f2OSVZ6yZP5ri6JjhHuHu4"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 71}, "component": "", "_componentId": "235dfPanl5Bo7YIOqwVsb3e", "handler": "slide<PERSON><PERSON><PERSON><PERSON>", "customEventData": ""}, {"__type__": "e0139aGBBFIBLYUi7ZvSe2q", "_name": "", "_objFlags": 0, "node": {"__id__": 137}, "_enabled": true, "_id": "5b74mcEEFHgqIW0+xIp7Dy"}, {"__type__": "cc.Slider", "_name": "", "_objFlags": 0, "node": {"__id__": 130}, "_enabled": true, "direction": 0, "slideEvents": [{"__id__": 143}], "_N$handle": {"__id__": 139}, "_N$progress": 1, "_id": "11t0dRoldMQoEUsm8cDi4h"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 130}, "component": "", "_componentId": "d720bc3df5BvI5vLHVhuwS9", "handler": "slideEvent", "customEventData": ""}, {"__type__": "d720bc3df5BvI5vLHVhuwS9", "_name": "", "_objFlags": 0, "node": {"__id__": 130}, "_enabled": true, "sliderBar": {"__id__": 134}, "sliderHandle": {"__id__": 137}, "_id": "abroMG02tJMrQ5QLZ/UVP0"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 130}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 34, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": "a5F/a73t5LNYqIidUxnxdF"}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 0, "_parent": {"__id__": 129}, "_children": [], "_active": true, "_components": [{"__id__": 147}, {"__id__": 148}, {"__id__": 149}, {"__id__": 150}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 90, "height": 37.8}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-128, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "45PYGq0IdMuJtaRTAITKEu"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 146}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_useOriginalSize": false, "_string": "音乐：", "_N$string": "音乐：", "_fontSize": 30, "_lineHeight": 30, "_enableWrapText": true, "_N$file": null, "_isSystemFontUsed": true, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 1, "_N$verticalAlign": 0, "_N$fontFamily": "华文琥珀", "_N$overflow": 0, "_N$cacheMode": 0, "_id": "e4E6TtItdKRLk4bLblH/IY"}, {"__type__": "cc.LabelShadow", "_name": "", "_objFlags": 0, "node": {"__id__": 146}, "_enabled": true, "_color": {"__type__": "cc.Color", "r": 59, "g": 59, "b": 59, "a": 255}, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_blur": 4, "_id": "7eikxvpOJBroNZBJbbUB9t"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 146}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 10, "_left": -3, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": "b6w4sFss5ML6D/0mEvNmak"}, {"__type__": "5520aFIq0hD17P+ce6P30KM", "_name": "", "_objFlags": 0, "node": {"__id__": 146}, "_enabled": true, "num": 0, "_id": "59pXx/DJpNmq5jjbULRXOL"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 129}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 2, "_left": 255, "_right": 255, "_top": 0, "_bottom": 0, "_verticalCenter": -26, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": "451WydIhpEz52QVOMaM1uy"}, {"__type__": "cc.Node", "_name": "closeButton", "_objFlags": 0, "_parent": {"__id__": 104}, "_children": [{"__id__": 153}], "_active": true, "_components": [{"__id__": 157}, {"__id__": 159}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 80, "height": 80}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [235, 110, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "c0dPkGz3hHdYhL3vokwY1S"}, {"__type__": "cc.Node", "_name": "closeButtonBg", "_objFlags": 512, "_parent": {"__id__": 152}, "_children": [], "_active": true, "_components": [{"__id__": 154}, {"__id__": 155}, {"__id__": 156}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 80, "height": 80}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "230j5rEVhOoKSAKk5tNdIJ"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 153}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "8cdb44ac-a3f6-449f-b354-7cd48cf84061"}, "_type": 1, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "b669jRMjlJwaqGAZqb/HmZ"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 153}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 100, "_originalHeight": 40, "_id": "0794Wjz7ZE/YzrNwyMM14z"}, {"__type__": "e0139aGBBFIBLYUi7ZvSe2q", "_name": "", "_objFlags": 0, "node": {"__id__": 153}, "_enabled": true, "_id": "78bhXpetlHppU9A0uLQZUT"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 152}, "_enabled": true, "_normalMaterial": {"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}, "_grayMaterial": null, "duration": 0.1, "zoomScale": 0.9, "clickEvents": [{"__id__": 158}], "_N$interactable": true, "_N$enableAutoGrayEffect": false, "_N$transition": 3, "transition": 3, "_N$normalColor": {"__type__": "cc.Color", "r": 230, "g": 230, "b": 230, "a": 255}, "_N$pressedColor": {"__type__": "cc.Color", "r": 200, "g": 200, "b": 200, "a": 255}, "pressedColor": {"__type__": "cc.Color", "r": 200, "g": 200, "b": 200, "a": 255}, "_N$hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$disabledColor": {"__type__": "cc.Color", "r": 120, "g": 120, "b": 120, "a": 200}, "_N$normalSprite": null, "_N$pressedSprite": null, "pressedSprite": null, "_N$hoverSprite": null, "hoverSprite": null, "_N$disabledSprite": null, "_N$target": {"__id__": 153}, "_id": "c4vO6lP/NLWYQXNK7Ggqux"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 71}, "component": "", "_componentId": "235dfPanl5Bo7YIOqwVsb3e", "handler": "closeButton", "customEventData": ""}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 152}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 33, "_left": 0, "_right": -20, "_top": -20, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": "31q6qiKNlKJJY2xV9zVOge"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 104}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "8cdb44ac-a3f6-449f-b354-7cd48cf84061"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "f9G2LV+VtP3oSnYVM8HgS9"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 104}, "_enabled": true, "alignMode": 2, "_target": null, "_alignFlags": 18, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": "e7uOgmD1NIqa57cW9FCa+c"}, {"__type__": "e0139aGBBFIBLYUi7ZvSe2q", "_name": "", "_objFlags": 0, "node": {"__id__": 104}, "_enabled": true, "_id": "1fIG1Ls6hC25sSFQsGH92M"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 99}, "_enabled": true, "alignMode": 2, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 100, "_originalHeight": 100, "_id": "28gqZjQ4lMapIKZ7jkfZpL"}, {"__type__": "cc.Node", "_name": "helpWin", "_objFlags": 0, "_parent": {"__id__": 4}, "_children": [{"__id__": 165}, {"__id__": 169}], "_active": false, "_components": [{"__id__": 412}, {"__id__": 413}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 2001, "height": 1125}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 4, "groupIndex": 4, "_id": "f4+PsN2iVAk7BlcIHi4vTm"}, {"__type__": "cc.Node", "_name": "grayBg", "_objFlags": 0, "_parent": {"__id__": 164}, "_children": [], "_active": true, "_components": [{"__id__": 166}, {"__id__": 167}, {"__id__": 168}], "_prefab": null, "_opacity": 0, "_color": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 2001, "height": 1125}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 4, "groupIndex": 4, "_id": "30TlPvdgZFE7QpvzqI+HyQ"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 165}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "a23235d1-15db-4b95-8439-a2e005bfff91"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "07iO/2U9lK1othftjmLZ1y"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 165}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 100, "_originalHeight": 100, "_id": "a4CFXl+1xFJr0POYQENWta"}, {"__type__": "cc.BlockInputEvents", "_name": "", "_objFlags": 0, "node": {"__id__": 165}, "_enabled": true, "_id": "1afkv4tURBUaheLCV9B8xl"}, {"__type__": "cc.Node", "_name": "help<PERSON>in<PERSON><PERSON>nt", "_objFlags": 0, "_parent": {"__id__": 164}, "_children": [{"__id__": 170}], "_active": true, "_components": [{"__id__": 411}], "_prefab": null, "_opacity": 0, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 1900, "height": 750}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -937.5, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 4, "groupIndex": 4, "_id": "fcegfLYKBE9JoV7NUnVyC9"}, {"__type__": "cc.Node", "_name": "helpWinBg", "_objFlags": 0, "_parent": {"__id__": 169}, "_children": [{"__id__": 171}, {"__id__": 371}, {"__id__": 378}, {"__id__": 400}], "_active": true, "_components": [{"__id__": 408}, {"__id__": 409}, {"__id__": 410}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 1480, "height": 500}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [190, -375, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 4, "groupIndex": 4, "_id": "9fdRK5WENEjq7LYJasemnH"}, {"__type__": "cc.Node", "_name": "pageList", "_objFlags": 0, "_parent": {"__id__": 170}, "_children": [{"__id__": 172}, {"__id__": 186}, {"__id__": 200}, {"__id__": 218}, {"__id__": 232}, {"__id__": 246}, {"__id__": 260}, {"__id__": 274}, {"__id__": 288}, {"__id__": 302}, {"__id__": 316}, {"__id__": 330}, {"__id__": 348}], "_active": true, "_components": [{"__id__": 370}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 1030, "height": 370}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [125, 285, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 4, "groupIndex": 4, "_id": "9a0068yOZI27ROOrEMJ4At"}, {"__type__": "cc.Node", "_name": "page0", "_objFlags": 0, "_parent": {"__id__": 171}, "_children": [{"__id__": 173}, {"__id__": 177}, {"__id__": 181}], "_active": true, "_components": [{"__id__": 185}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 1030, "height": 370}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 4, "groupIndex": 4, "_id": "63i+mZ9jZF+olFCkyffIW8"}, {"__type__": "cc.Node", "_name": "title", "_objFlags": 0, "_parent": {"__id__": 172}, "_children": [], "_active": true, "_components": [{"__id__": 174}, {"__id__": 175}, {"__id__": 176}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 141, "g": 97, "b": 98, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 120, "height": 75.6}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-515, 147.2, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 4, "groupIndex": 4, "_id": "49j9RgO6FBSLeahkIMXweX"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 173}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_useOriginalSize": true, "_string": "划线", "_N$string": "划线", "_fontSize": 60, "_lineHeight": 60, "_enableWrapText": true, "_N$file": null, "_isSystemFontUsed": true, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 0, "_N$verticalAlign": 1, "_N$fontFamily": "华文琥珀", "_N$overflow": 0, "_N$cacheMode": 0, "_id": "2e8h0sxj1JSIaZiulW+j8R"}, {"__type__": "5520aFIq0hD17P+ce6P30KM", "_name": "", "_objFlags": 0, "node": {"__id__": 173}, "_enabled": true, "num": 0, "_id": "19c4SvolRA2avhS23x1Rj+"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 173}, "_enabled": true, "alignMode": 2, "_target": null, "_alignFlags": 9, "_left": 0, "_right": 910, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 120, "_originalHeight": 0, "_id": "51DfwKCBlGJbDbrrTdiaUH"}, {"__type__": "cc.Node", "_name": "content", "_objFlags": 0, "_parent": {"__id__": 172}, "_children": [], "_active": true, "_components": [{"__id__": 178}, {"__id__": 179}, {"__id__": 180}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 141, "g": 97, "b": 98, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 780, "height": 250}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-125, -60, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 4, "groupIndex": 4, "_id": "8522+v3LdAX4D9UVWpotnL"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 177}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_useOriginalSize": true, "_string": "你需要在屏幕上划线来生成新的平台！\n果冻会根据平台的角度进行不同的碰撞跳跃。", "_N$string": "你需要在屏幕上划线来生成新的平台！\n果冻会根据平台的角度进行不同的碰撞跳跃。", "_fontSize": 35, "_lineHeight": 50, "_enableWrapText": true, "_N$file": null, "_isSystemFontUsed": true, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 1, "_underlineHeight": 0, "_N$horizontalAlign": 0, "_N$verticalAlign": 0, "_N$fontFamily": "幼圆", "_N$overflow": 2, "_N$cacheMode": 0, "_id": "9dcZmrDEJOU7FDawOfq9Oz"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 177}, "_enabled": true, "alignMode": 2, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 250, "_top": 120, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": "0bQOnt2QZIRIcrLBMZlj+O"}, {"__type__": "5520aFIq0hD17P+ce6P30KM", "_name": "", "_objFlags": 0, "node": {"__id__": 177}, "_enabled": true, "num": 1, "_id": "99i5H89MZCS66L+HhWTCQu"}, {"__type__": "cc.Node", "_name": "helpImg_HX", "_objFlags": 0, "_parent": {"__id__": 172}, "_children": [], "_active": true, "_components": [{"__id__": 182}, {"__id__": 183}, {"__id__": 184}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 264, "height": 303}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [383, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "ffAhFSLm1MfLOno1ljfBHE"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 181}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "8cdb44ac-a3f6-449f-b354-7cd48cf84061"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": null, "_id": "69qNIT7fpLg5P8ebgIodlW"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 181}, "_enabled": true, "alignMode": 2, "_target": null, "_alignFlags": 34, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": "c7GMBlIHlGHLWm1Zk2AHVN"}, {"__type__": "e0139aGBBFIBLYUi7ZvSe2q", "_name": "", "_objFlags": 0, "node": {"__id__": 181}, "_enabled": true, "_id": "dfO1sBRQNBlYDmWCZOmkn3"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 172}, "_enabled": true, "alignMode": 2, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": "95wK02CWxMLboLd9zqqSTp"}, {"__type__": "cc.Node", "_name": "page1", "_objFlags": 0, "_parent": {"__id__": 171}, "_children": [{"__id__": 187}, {"__id__": 191}, {"__id__": 195}], "_active": false, "_components": [{"__id__": 199}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 1030, "height": 370}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 4, "groupIndex": 4, "_id": "6f9jO7mJNHwbPH26wzQDJ4"}, {"__type__": "cc.Node", "_name": "title", "_objFlags": 0, "_parent": {"__id__": 186}, "_children": [], "_active": true, "_components": [{"__id__": 188}, {"__id__": 189}, {"__id__": 190}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 141, "g": 97, "b": 98, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 240, "height": 75.6}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-515, 147.2, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 4, "groupIndex": 4, "_id": "78SDjEyu1K16C5b+XgmQcZ"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 187}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_useOriginalSize": true, "_string": "划线限制", "_N$string": "划线限制", "_fontSize": 60, "_lineHeight": 60, "_enableWrapText": true, "_N$file": null, "_isSystemFontUsed": true, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 0, "_N$verticalAlign": 1, "_N$fontFamily": "华文琥珀", "_N$overflow": 0, "_N$cacheMode": 0, "_id": "03K0BJmqlDS7UJlLBaOJrt"}, {"__type__": "5520aFIq0hD17P+ce6P30KM", "_name": "", "_objFlags": 0, "node": {"__id__": 187}, "_enabled": true, "num": 0, "_id": "e1DVG1dhpIypMUUgiPQEse"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 187}, "_enabled": true, "alignMode": 2, "_target": null, "_alignFlags": 9, "_left": 0, "_right": 910, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 120, "_originalHeight": 0, "_id": "39HtACIjJE+ZyherMBzzTU"}, {"__type__": "cc.Node", "_name": "content", "_objFlags": 0, "_parent": {"__id__": 186}, "_children": [], "_active": true, "_components": [{"__id__": 192}, {"__id__": 193}, {"__id__": 194}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 141, "g": 97, "b": 98, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 1030, "height": 250}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -60, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 4, "groupIndex": 4, "_id": "ea36oTPmRG2psCOcc4ZJ6w"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 191}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_useOriginalSize": true, "_string": "划线时不能与其他组件重叠，划线长度不能太长或太短！\n能量值也要足够！否则将无法成功创建平台！", "_N$string": "划线时不能与其他组件重叠，划线长度不能太长或太短！\n能量值也要足够！否则将无法成功创建平台！", "_fontSize": 35, "_lineHeight": 50, "_enableWrapText": true, "_N$file": null, "_isSystemFontUsed": true, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 1, "_underlineHeight": 0, "_N$horizontalAlign": 0, "_N$verticalAlign": 0, "_N$fontFamily": "幼圆", "_N$overflow": 2, "_N$cacheMode": 0, "_id": "76ZZEvhPRAoKICDV5o3TAo"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 191}, "_enabled": true, "alignMode": 2, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 0, "_top": 120, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": "c9ZSonBppDMqZraD9AMPNz"}, {"__type__": "5520aFIq0hD17P+ce6P30KM", "_name": "", "_objFlags": 0, "node": {"__id__": 191}, "_enabled": true, "num": 1, "_id": "092FUtxUZJQYr7ft7lGZZP"}, {"__type__": "cc.Node", "_name": "helpImg_HXXZ", "_objFlags": 0, "_parent": {"__id__": 186}, "_children": [], "_active": true, "_components": [{"__id__": 196}, {"__id__": 197}, {"__id__": 198}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 866, "height": 120}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-82, -105, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "d3PC7hQb1Nc7u06y/KsWGc"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 195}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "8cdb44ac-a3f6-449f-b354-7cd48cf84061"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": null, "_id": "e6oTlXFXFG6ImPEjACEaiK"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 195}, "_enabled": true, "alignMode": 2, "_target": null, "_alignFlags": 12, "_left": 0, "_right": 368.409, "_top": 0, "_bottom": 20, "_verticalCenter": -109.248, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": "e7hWNKDqlFV65RD1f5Mri9"}, {"__type__": "e0139aGBBFIBLYUi7ZvSe2q", "_name": "", "_objFlags": 0, "node": {"__id__": 195}, "_enabled": true, "_id": "fdTt0nx3ZF/LPNpDxe+284"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 186}, "_enabled": true, "alignMode": 2, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": "3d8E+tTSdH2IYYMEt82Scu"}, {"__type__": "cc.Node", "_name": "page2", "_objFlags": 0, "_parent": {"__id__": 171}, "_children": [{"__id__": 201}, {"__id__": 205}, {"__id__": 209}, {"__id__": 213}], "_active": false, "_components": [{"__id__": 217}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 1030, "height": 370}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 4, "groupIndex": 4, "_id": "baKHF37FlIjYZb0D8Be+E/"}, {"__type__": "cc.Node", "_name": "title", "_objFlags": 0, "_parent": {"__id__": 200}, "_children": [], "_active": true, "_components": [{"__id__": 202}, {"__id__": 203}, {"__id__": 204}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 141, "g": 97, "b": 98, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 180, "height": 75.6}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-515, 147.2, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 4, "groupIndex": 4, "_id": "94bVWnYKlGNYkPw+uTCMAS"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 201}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_useOriginalSize": true, "_string": "能量值", "_N$string": "能量值", "_fontSize": 60, "_lineHeight": 60, "_enableWrapText": true, "_N$file": null, "_isSystemFontUsed": true, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 0, "_N$verticalAlign": 1, "_N$fontFamily": "华文琥珀", "_N$overflow": 0, "_N$cacheMode": 0, "_id": "fevsagUYVFT6HTWw/MUsAT"}, {"__type__": "5520aFIq0hD17P+ce6P30KM", "_name": "", "_objFlags": 0, "node": {"__id__": 201}, "_enabled": true, "num": 0, "_id": "ffjDKOKrNG2LANiIx9KtKV"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 201}, "_enabled": true, "alignMode": 2, "_target": null, "_alignFlags": 9, "_left": 0, "_right": 910, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 120, "_originalHeight": 0, "_id": "f2eXiEjlZHbZ5MLkB5eaAN"}, {"__type__": "cc.Node", "_name": "content", "_objFlags": 0, "_parent": {"__id__": 200}, "_children": [], "_active": true, "_components": [{"__id__": 206}, {"__id__": 207}, {"__id__": 208}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 141, "g": 97, "b": 98, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 1040, "height": 250}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [5, -60, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 4, "groupIndex": 4, "_id": "31Zdju/OpA3JdvwSf0rJuW"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 205}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_useOriginalSize": true, "_string": "平台的生成需要一定的能量，长度越长的平台消耗的能量越多！\n能量会随着时间缓慢增加，能量不足时将无法创建平台！", "_N$string": "平台的生成需要一定的能量，长度越长的平台消耗的能量越多！\n能量会随着时间缓慢增加，能量不足时将无法创建平台！", "_fontSize": 35, "_lineHeight": 50, "_enableWrapText": true, "_N$file": null, "_isSystemFontUsed": true, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 1, "_underlineHeight": 0, "_N$horizontalAlign": 0, "_N$verticalAlign": 0, "_N$fontFamily": "幼圆", "_N$overflow": 2, "_N$cacheMode": 0, "_id": "a39s0QuDFLk7euHgRO8GHs"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 205}, "_enabled": true, "alignMode": 2, "_target": null, "_alignFlags": 45, "_left": 0, "_right": -75, "_top": 120, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": "547JKYnqtJWo6pV+BG4K7Y"}, {"__type__": "5520aFIq0hD17P+ce6P30KM", "_name": "", "_objFlags": 0, "node": {"__id__": 205}, "_enabled": true, "num": 1, "_id": "e8BUG+1VZEyJNyVaNMa3dR"}, {"__type__": "cc.Node", "_name": "helpImg_NLZD", "_objFlags": 0, "_parent": {"__id__": 200}, "_children": [], "_active": true, "_components": [{"__id__": 210}, {"__id__": 211}, {"__id__": 212}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 277, "height": 128}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-346.5, -121, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "71eg0jpOJIKL+cccQRupQp"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 209}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "8cdb44ac-a3f6-449f-b354-7cd48cf84061"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": null, "_id": "8affvjsnVDiIow9PGo/QpQ"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 209}, "_enabled": true, "alignMode": 2, "_target": null, "_alignFlags": 12, "_left": 30, "_right": 753, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": -300, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 277, "_originalHeight": 0, "_id": "0fvqZkrohK9qcJOl0EoHqX"}, {"__type__": "e0139aGBBFIBLYUi7ZvSe2q", "_name": "", "_objFlags": 0, "node": {"__id__": 209}, "_enabled": true, "_id": "319jWKNU9O/JCuI0hN60ci"}, {"__type__": "cc.Node", "_name": "helpImg_NLZC", "_objFlags": 0, "_parent": {"__id__": 200}, "_children": [], "_active": true, "_components": [{"__id__": 214}, {"__id__": 215}, {"__id__": 216}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 331, "height": 128}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [100, -121, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "50Epb7+dtIFY9ICF2z7L5t"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 213}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "8cdb44ac-a3f6-449f-b354-7cd48cf84061"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": null, "_id": "1byaa6xupCD4D3LnIMnabj"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 213}, "_enabled": true, "alignMode": 2, "_target": null, "_alignFlags": 20, "_left": 300, "_right": -33.5, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 100, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": "71IKBNqhhEoY/DK826VCqa"}, {"__type__": "e0139aGBBFIBLYUi7ZvSe2q", "_name": "", "_objFlags": 0, "node": {"__id__": 213}, "_enabled": true, "_id": "74tyGasWJI67TZ6CzLWkrf"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 200}, "_enabled": true, "alignMode": 2, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": "01o4nUCSFNsbXufRTvseTL"}, {"__type__": "cc.Node", "_name": "page3", "_objFlags": 0, "_parent": {"__id__": 171}, "_children": [{"__id__": 219}, {"__id__": 223}, {"__id__": 227}], "_active": false, "_components": [{"__id__": 231}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 1030, "height": 370}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 4, "groupIndex": 4, "_id": "5eYnDn7nVDeZR2mWU/q5FQ"}, {"__type__": "cc.Node", "_name": "title", "_objFlags": 0, "_parent": {"__id__": 218}, "_children": [], "_active": true, "_components": [{"__id__": 220}, {"__id__": 221}, {"__id__": 222}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 141, "g": 97, "b": 98, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 180, "height": 75.6}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-515, 147.2, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 4, "groupIndex": 4, "_id": "9eyGX/HWZJXIy1kx+6Bg58"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 219}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_useOriginalSize": true, "_string": "障碍物", "_N$string": "障碍物", "_fontSize": 60, "_lineHeight": 60, "_enableWrapText": true, "_N$file": null, "_isSystemFontUsed": true, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 0, "_N$verticalAlign": 1, "_N$fontFamily": "华文琥珀", "_N$overflow": 0, "_N$cacheMode": 0, "_id": "3dD2Hiq3dEu7fOtu7nsX3t"}, {"__type__": "5520aFIq0hD17P+ce6P30KM", "_name": "", "_objFlags": 0, "node": {"__id__": 219}, "_enabled": true, "num": 0, "_id": "a1nxbEA2pIMpWdEe/o5GoY"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 219}, "_enabled": true, "alignMode": 2, "_target": null, "_alignFlags": 9, "_left": 0, "_right": 910, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 120, "_originalHeight": 0, "_id": "c6xUmp5CFFJpPpxI9FStoQ"}, {"__type__": "cc.Node", "_name": "content", "_objFlags": 0, "_parent": {"__id__": 218}, "_children": [], "_active": true, "_components": [{"__id__": 224}, {"__id__": 225}, {"__id__": 226}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 141, "g": 97, "b": 98, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 770, "height": 250}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-130, -60, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 4, "groupIndex": 4, "_id": "2f7F7gsOVP561xeyvZ8hBC"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 223}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_useOriginalSize": true, "_string": "果冻的世界里不止有地面是危险的，一些空中的生物也是致命的障碍物，请一边注意一边往上前进吧！", "_N$string": "果冻的世界里不止有地面是危险的，一些空中的生物也是致命的障碍物，请一边注意一边往上前进吧！", "_fontSize": 35, "_lineHeight": 50, "_enableWrapText": true, "_N$file": null, "_isSystemFontUsed": true, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 1, "_underlineHeight": 0, "_N$horizontalAlign": 0, "_N$verticalAlign": 0, "_N$fontFamily": "幼圆", "_N$overflow": 2, "_N$cacheMode": 0, "_id": "77/Kw12hRO5rGWxe5p8gaY"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 223}, "_enabled": true, "alignMode": 2, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 260, "_top": 120, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": "02X2han9VGuKjLKK6E3vq5"}, {"__type__": "5520aFIq0hD17P+ce6P30KM", "_name": "", "_objFlags": 0, "node": {"__id__": 223}, "_enabled": true, "num": 1, "_id": "8bu7QTyUVJoq56a84iWb5b"}, {"__type__": "cc.Node", "_name": "helpImg_ZAW", "_objFlags": 0, "_parent": {"__id__": 218}, "_children": [], "_active": true, "_components": [{"__id__": 228}, {"__id__": 229}, {"__id__": 230}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 325, "height": 385}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [385, 0, 0, 0, 0, 0, 1, 0.8, 0.8, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "55GTYabz9BYIxWELiwSDQf"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 227}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "8cdb44ac-a3f6-449f-b354-7cd48cf84061"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": null, "_id": "dfgFiDvANBH6UHR0fOdWWc"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 227}, "_enabled": true, "alignMode": 2, "_target": null, "_alignFlags": 34, "_left": 729.4780000000001, "_right": 0, "_top": 0, "_bottom": 26.94400000000001, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": "63a/DmPAZOjYNztuILlIUH"}, {"__type__": "e0139aGBBFIBLYUi7ZvSe2q", "_name": "", "_objFlags": 0, "node": {"__id__": 227}, "_enabled": true, "_id": "75jB8AJzlEG72B3rCYRn2T"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 218}, "_enabled": true, "alignMode": 2, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": "3bwzfY/sNOJIW7Phz94cW/"}, {"__type__": "cc.Node", "_name": "page4", "_objFlags": 0, "_parent": {"__id__": 171}, "_children": [{"__id__": 233}, {"__id__": 237}, {"__id__": 241}], "_active": false, "_components": [{"__id__": 245}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 1030, "height": 370}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 4, "groupIndex": 4, "_id": "32N2fPJcdM24VrI+RjZocd"}, {"__type__": "cc.Node", "_name": "title", "_objFlags": 0, "_parent": {"__id__": 232}, "_children": [], "_active": true, "_components": [{"__id__": 234}, {"__id__": 235}, {"__id__": 236}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 141, "g": 97, "b": 98, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 420, "height": 75.6}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-515, 147.2, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 4, "groupIndex": 4, "_id": "d4ciUj2+RHs4d9K4mdn72b"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 233}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_useOriginalSize": true, "_string": "小黄鸡与榨汁鸡", "_N$string": "小黄鸡与榨汁鸡", "_fontSize": 60, "_lineHeight": 60, "_enableWrapText": true, "_N$file": null, "_isSystemFontUsed": true, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 0, "_N$verticalAlign": 1, "_N$fontFamily": "华文琥珀", "_N$overflow": 0, "_N$cacheMode": 0, "_id": "54JKuNzYpC74yEzwE1HLKG"}, {"__type__": "5520aFIq0hD17P+ce6P30KM", "_name": "", "_objFlags": 0, "node": {"__id__": 233}, "_enabled": true, "num": 0, "_id": "e6QDYypUhOtZvyRCNbX4bp"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 233}, "_enabled": true, "alignMode": 2, "_target": null, "_alignFlags": 9, "_left": 0, "_right": 910, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 120, "_originalHeight": 0, "_id": "96n+mQdXRLn5P2Zvhvpajp"}, {"__type__": "cc.Node", "_name": "content", "_objFlags": 0, "_parent": {"__id__": 232}, "_children": [], "_active": true, "_components": [{"__id__": 238}, {"__id__": 239}, {"__id__": 240}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 141, "g": 97, "b": 98, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 770, "height": 250}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-130, -60, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 4, "groupIndex": 4, "_id": "94UTtWhItD8Jy0hHb7jdEM"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 237}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_useOriginalSize": true, "_string": "外表圆滚滚看上去十分可爱的家伙叫做小黄鸡，它并不像表面上那般人畜无害，遇上它会被野蛮地撞开!\n红色的家伙名叫榨汁鸡，它的性格十分暴躁，碰上它会被它尖锐的爪牙撕碎！", "_N$string": "外表圆滚滚看上去十分可爱的家伙叫做小黄鸡，它并不像表面上那般人畜无害，遇上它会被野蛮地撞开!\n红色的家伙名叫榨汁鸡，它的性格十分暴躁，碰上它会被它尖锐的爪牙撕碎！", "_fontSize": 35, "_lineHeight": 50, "_enableWrapText": true, "_N$file": null, "_isSystemFontUsed": true, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 1, "_underlineHeight": 0, "_N$horizontalAlign": 0, "_N$verticalAlign": 0, "_N$fontFamily": "幼圆", "_N$overflow": 2, "_N$cacheMode": 0, "_id": "9fpTEfUdRJfLxFB26QoAdU"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 237}, "_enabled": true, "alignMode": 2, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 260, "_top": 120, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": "2aOEmVUPlNC4JMyzPOYfn2"}, {"__type__": "5520aFIq0hD17P+ce6P30KM", "_name": "", "_objFlags": 0, "node": {"__id__": 237}, "_enabled": true, "num": 1, "_id": "6aVuwy95dC47qdQUxUGJUU"}, {"__type__": "cc.Node", "_name": "helpImg_LZJ", "_objFlags": 0, "_parent": {"__id__": 232}, "_children": [], "_active": true, "_components": [{"__id__": 242}, {"__id__": 243}, {"__id__": 244}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 255, "height": 182}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [387.5, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "cc0DD2FUJBfZvLQZ/djJ/e"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 241}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "8cdb44ac-a3f6-449f-b354-7cd48cf84061"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": null, "_id": "f1+EDs1FxNn7/oYXgVqlZN"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 241}, "_enabled": true, "alignMode": 2, "_target": null, "_alignFlags": 34, "_left": 729.4780000000001, "_right": 0, "_top": 0, "_bottom": 26.94400000000001, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": "74VMvmZfRB+6IJwm4tWcLH"}, {"__type__": "e0139aGBBFIBLYUi7ZvSe2q", "_name": "", "_objFlags": 0, "node": {"__id__": 241}, "_enabled": true, "_id": "a6cmPCD0xPuLVou0unDy2I"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 232}, "_enabled": true, "alignMode": 2, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": "10O47TbmBKDpiEpVNXFeOf"}, {"__type__": "cc.Node", "_name": "page5", "_objFlags": 0, "_parent": {"__id__": 171}, "_children": [{"__id__": 247}, {"__id__": 251}, {"__id__": 255}], "_active": false, "_components": [{"__id__": 259}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 1030, "height": 370}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 4, "groupIndex": 4, "_id": "0c39iU1vtAY5t8vpIQqCFu"}, {"__type__": "cc.Node", "_name": "title", "_objFlags": 0, "_parent": {"__id__": 246}, "_children": [], "_active": true, "_components": [{"__id__": 248}, {"__id__": 249}, {"__id__": 250}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 141, "g": 97, "b": 98, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 180, "height": 75.6}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-515, 147.2, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 4, "groupIndex": 4, "_id": "a81LDVvnFKcpk6qs7VLo54"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 247}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_useOriginalSize": true, "_string": "果冻杯", "_N$string": "果冻杯", "_fontSize": 60, "_lineHeight": 60, "_enableWrapText": true, "_N$file": null, "_isSystemFontUsed": true, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 0, "_N$verticalAlign": 1, "_N$fontFamily": "华文琥珀", "_N$overflow": 0, "_N$cacheMode": 0, "_id": "453PJok0lOfahwoqQ2h+w7"}, {"__type__": "5520aFIq0hD17P+ce6P30KM", "_name": "", "_objFlags": 0, "node": {"__id__": 247}, "_enabled": true, "num": 0, "_id": "0eiUr8cNpG8bRisYGYdDuh"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 247}, "_enabled": true, "alignMode": 2, "_target": null, "_alignFlags": 9, "_left": 0, "_right": 910, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 120, "_originalHeight": 0, "_id": "34vbb5LJpHUZjbxq+WCRXm"}, {"__type__": "cc.Node", "_name": "content", "_objFlags": 0, "_parent": {"__id__": 246}, "_children": [], "_active": true, "_components": [{"__id__": 252}, {"__id__": 253}, {"__id__": 254}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 141, "g": 97, "b": 98, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 770, "height": 250}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-130, -60, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 4, "groupIndex": 4, "_id": "87kcihiBZLxYCrCEn0Byck"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 251}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_useOriginalSize": true, "_string": "看上去十分可口的一杯果冻，遇上了绝对不能错过！\n食用可获得一个随机的皮肤碎片！收集足够的皮肤碎片可更换果冻以及平台的外观！", "_N$string": "看上去十分可口的一杯果冻，遇上了绝对不能错过！\n食用可获得一个随机的皮肤碎片！收集足够的皮肤碎片可更换果冻以及平台的外观！", "_fontSize": 35, "_lineHeight": 50, "_enableWrapText": true, "_N$file": null, "_isSystemFontUsed": true, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 1, "_underlineHeight": 0, "_N$horizontalAlign": 0, "_N$verticalAlign": 0, "_N$fontFamily": "幼圆", "_N$overflow": 2, "_N$cacheMode": 0, "_id": "48tXPIgs5Lcp5XZnD0Iy7V"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 251}, "_enabled": true, "alignMode": 2, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 260, "_top": 120, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": "6dMwlKAfFKgojm/eVAR3v7"}, {"__type__": "5520aFIq0hD17P+ce6P30KM", "_name": "", "_objFlags": 0, "node": {"__id__": 251}, "_enabled": true, "num": 1, "_id": "5dVJSmpqxJRKN7Q3O3cY2q"}, {"__type__": "cc.Node", "_name": "jelly<PERSON><PERSON><PERSON>", "_objFlags": 0, "_parent": {"__id__": 246}, "_children": [], "_active": true, "_components": [{"__id__": 256}, {"__id__": 257}, {"__id__": 258}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 663, "height": 584}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [402.4, 0, 0, 0, 0, 0, 1, 0.4, 0.4, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "a8bs4wgRlOGrSckPv/W9hv"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 255}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "8cdb44ac-a3f6-449f-b354-7cd48cf84061"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": null, "_id": "95G72zfrxIFb8I9jI4HY3Y"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 255}, "_enabled": true, "alignMode": 2, "_target": null, "_alignFlags": 34, "_left": 729.4780000000001, "_right": -20, "_top": 0, "_bottom": 26.94400000000001, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": "9eZmEVVBdL+K/d1pnF70hJ"}, {"__type__": "e0139aGBBFIBLYUi7ZvSe2q", "_name": "", "_objFlags": 0, "node": {"__id__": 255}, "_enabled": true, "_id": "71y1sY79tGloFBW6k5rbH0"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 246}, "_enabled": true, "alignMode": 2, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": "0ae9D3xOpIPJNKxmFtBNep"}, {"__type__": "cc.Node", "_name": "page6", "_objFlags": 0, "_parent": {"__id__": 171}, "_children": [{"__id__": 261}, {"__id__": 265}, {"__id__": 269}], "_active": false, "_components": [{"__id__": 273}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 1030, "height": 370}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 4, "groupIndex": 4, "_id": "0f+gt6zmZJd5W2MYQSqJXM"}, {"__type__": "cc.Node", "_name": "title", "_objFlags": 0, "_parent": {"__id__": 260}, "_children": [], "_active": true, "_components": [{"__id__": 262}, {"__id__": 263}, {"__id__": 264}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 141, "g": 97, "b": 98, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 240, "height": 75.6}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-515, 147.2, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 4, "groupIndex": 4, "_id": "dcMqduWzFNrYP2VCyFJcHs"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 261}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_useOriginalSize": true, "_string": "果冻泡泡", "_N$string": "果冻泡泡", "_fontSize": 60, "_lineHeight": 60, "_enableWrapText": true, "_N$file": null, "_isSystemFontUsed": true, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 0, "_N$verticalAlign": 1, "_N$fontFamily": "华文琥珀", "_N$overflow": 0, "_N$cacheMode": 0, "_id": "3acAHUBSZBfrkTQfTQQr2L"}, {"__type__": "5520aFIq0hD17P+ce6P30KM", "_name": "", "_objFlags": 0, "node": {"__id__": 261}, "_enabled": true, "num": 0, "_id": "abggz0Tn1NiqPebW+Qs9Ef"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 261}, "_enabled": true, "alignMode": 2, "_target": null, "_alignFlags": 9, "_left": 0, "_right": 910, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 120, "_originalHeight": 0, "_id": "26rsl5uTxOdI5P0AAvKSIR"}, {"__type__": "cc.Node", "_name": "content", "_objFlags": 0, "_parent": {"__id__": 260}, "_children": [], "_active": true, "_components": [{"__id__": 266}, {"__id__": 267}, {"__id__": 268}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 141, "g": 97, "b": 98, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 770, "height": 250}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-130, -60, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 4, "groupIndex": 4, "_id": "6dT03hj5pGMqwyqCqPhAq6"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 265}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_useOriginalSize": true, "_string": "这些包裹着不同果冻的泡泡会不停的在你周围出现，没人知道它究竟从哪来的。\n吃掉这些泡泡会出现意想不到的效果哦！", "_N$string": "这些包裹着不同果冻的泡泡会不停的在你周围出现，没人知道它究竟从哪来的。\n吃掉这些泡泡会出现意想不到的效果哦！", "_fontSize": 35, "_lineHeight": 50, "_enableWrapText": true, "_N$file": null, "_isSystemFontUsed": true, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 1, "_underlineHeight": 0, "_N$horizontalAlign": 0, "_N$verticalAlign": 0, "_N$fontFamily": "幼圆", "_N$overflow": 2, "_N$cacheMode": 0, "_id": "d8WIQZ9b5Gs7SrtFIRoKmH"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 265}, "_enabled": true, "alignMode": 2, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 260, "_top": 120, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": "9b34R3Ad5E0Iwdk8yMxo0V"}, {"__type__": "5520aFIq0hD17P+ce6P30KM", "_name": "", "_objFlags": 0, "node": {"__id__": 265}, "_enabled": true, "num": 1, "_id": "e2oPxFXM5AhYEY7dTP9aOl"}, {"__type__": "cc.Node", "_name": "helpImg_GDPP", "_objFlags": 0, "_parent": {"__id__": 260}, "_children": [], "_active": true, "_components": [{"__id__": 270}, {"__id__": 271}, {"__id__": 272}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 574, "height": 304}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [342.8, 0, 0, 0, 0, 0, 1, 0.6, 0.6, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "e10b/BZkVAMZFuuPWnp9Nj"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 269}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "8cdb44ac-a3f6-449f-b354-7cd48cf84061"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": null, "_id": "58G+shwtNNPo1G6/r7X9Eu"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 269}, "_enabled": true, "alignMode": 2, "_target": null, "_alignFlags": 34, "_left": 729.4780000000001, "_right": 0, "_top": 0, "_bottom": 26.94400000000001, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": "560IsF2HdI57NnHKIL59O6"}, {"__type__": "e0139aGBBFIBLYUi7ZvSe2q", "_name": "", "_objFlags": 0, "node": {"__id__": 269}, "_enabled": true, "_id": "7eEnhy3GRDP6A63uPP3Ttw"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 260}, "_enabled": true, "alignMode": 2, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": "a1NUQU0hlPn7Ef/f8dlOAC"}, {"__type__": "cc.Node", "_name": "page7", "_objFlags": 0, "_parent": {"__id__": 171}, "_children": [{"__id__": 275}, {"__id__": 279}, {"__id__": 283}], "_active": false, "_components": [{"__id__": 287}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 1030, "height": 370}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 4, "groupIndex": 4, "_id": "81wE1fZcFEEZTaXmLpj3hL"}, {"__type__": "cc.Node", "_name": "title", "_objFlags": 0, "_parent": {"__id__": 274}, "_children": [], "_active": true, "_components": [{"__id__": 276}, {"__id__": 277}, {"__id__": 278}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 141, "g": 97, "b": 98, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 240, "height": 75.6}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-515, 147.2, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 4, "groupIndex": 4, "_id": "a9zwH7zL1BxpXSXEoYDZea"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 275}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_useOriginalSize": true, "_string": "火箭泡泡", "_N$string": "火箭泡泡", "_fontSize": 60, "_lineHeight": 60, "_enableWrapText": true, "_N$file": null, "_isSystemFontUsed": true, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 0, "_N$verticalAlign": 1, "_N$fontFamily": "华文琥珀", "_N$overflow": 0, "_N$cacheMode": 0, "_id": "a6LOPi/yxLK5N0yniybV9q"}, {"__type__": "5520aFIq0hD17P+ce6P30KM", "_name": "", "_objFlags": 0, "node": {"__id__": 275}, "_enabled": true, "num": 0, "_id": "d9TUPn3B5JlrNDYuDabqKM"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 275}, "_enabled": true, "alignMode": 2, "_target": null, "_alignFlags": 9, "_left": 0, "_right": 910, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 120, "_originalHeight": 0, "_id": "8ed8RIujVLpITAa8wFrqoq"}, {"__type__": "cc.Node", "_name": "content", "_objFlags": 0, "_parent": {"__id__": 274}, "_children": [], "_active": true, "_components": [{"__id__": 280}, {"__id__": 281}, {"__id__": 282}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 141, "g": 97, "b": 98, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 800, "height": 250}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-115, -60, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 4, "groupIndex": 4, "_id": "b5V5aUD7VBPYMCvZgVWGqT"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 279}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_useOriginalSize": true, "_string": "包裹着火箭果冻的泡泡，食用后会在短时间内获得像火箭一般的爆发力，但是很容易因为用力过猛带来麻烦！", "_N$string": "包裹着火箭果冻的泡泡，食用后会在短时间内获得像火箭一般的爆发力，但是很容易因为用力过猛带来麻烦！", "_fontSize": 35, "_lineHeight": 50, "_enableWrapText": true, "_N$file": null, "_isSystemFontUsed": true, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 1, "_underlineHeight": 0, "_N$horizontalAlign": 0, "_N$verticalAlign": 0, "_N$fontFamily": "幼圆", "_N$overflow": 2, "_N$cacheMode": 0, "_id": "e1Mzr1iSxL3aBgRC1dSbAh"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 279}, "_enabled": true, "alignMode": 2, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 230, "_top": 120, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": "58pu0SzCZOGIDoLH/NcH3K"}, {"__type__": "5520aFIq0hD17P+ce6P30KM", "_name": "", "_objFlags": 0, "node": {"__id__": 279}, "_enabled": true, "num": 1, "_id": "10EfFLQ3BERKb7gbzJtiet"}, {"__type__": "cc.Node", "_name": "speedProp", "_objFlags": 0, "_parent": {"__id__": 274}, "_children": [], "_active": true, "_components": [{"__id__": 284}, {"__id__": 285}, {"__id__": 286}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 252, "height": 252}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [414.2, 0, 0, 0, 0, 0, 1, 0.8, 0.8, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "afZqggIrVETL51texMliwe"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 283}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "8cdb44ac-a3f6-449f-b354-7cd48cf84061"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": null, "_id": "cfLWq9jGdA7ISVdbtxlQLv"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 283}, "_enabled": true, "alignMode": 2, "_target": null, "_alignFlags": 34, "_left": 729.4780000000001, "_right": 0, "_top": 0, "_bottom": 26.94400000000001, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": "d8IG7T811NpJ/GfQJPDNxq"}, {"__type__": "e0139aGBBFIBLYUi7ZvSe2q", "_name": "", "_objFlags": 0, "node": {"__id__": 283}, "_enabled": true, "_id": "97ta0Vl2FEjbx9evqYePZm"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 274}, "_enabled": true, "alignMode": 2, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": "96HQXXTXtB7LvGokMW5jmp"}, {"__type__": "cc.Node", "_name": "page8", "_objFlags": 0, "_parent": {"__id__": 171}, "_children": [{"__id__": 289}, {"__id__": 293}, {"__id__": 297}], "_active": false, "_components": [{"__id__": 301}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 1030, "height": 370}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 4, "groupIndex": 4, "_id": "59Ipzid9xDJr/JjoBTmMkD"}, {"__type__": "cc.Node", "_name": "title", "_objFlags": 0, "_parent": {"__id__": 288}, "_children": [], "_active": true, "_components": [{"__id__": 290}, {"__id__": 291}, {"__id__": 292}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 141, "g": 97, "b": 98, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 240, "height": 75.6}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-515, 147.2, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 4, "groupIndex": 4, "_id": "ea8ZHSRl1BmatqD8lavigs"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 289}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_useOriginalSize": true, "_string": "能量泡泡", "_N$string": "能量泡泡", "_fontSize": 60, "_lineHeight": 60, "_enableWrapText": true, "_N$file": null, "_isSystemFontUsed": true, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 0, "_N$verticalAlign": 1, "_N$fontFamily": "华文琥珀", "_N$overflow": 0, "_N$cacheMode": 0, "_id": "f4z9X49uxM6bNsf/rdFrKM"}, {"__type__": "5520aFIq0hD17P+ce6P30KM", "_name": "", "_objFlags": 0, "node": {"__id__": 289}, "_enabled": true, "num": 0, "_id": "51IFW59RRB1ovrw8U+wHnc"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 289}, "_enabled": true, "alignMode": 2, "_target": null, "_alignFlags": 9, "_left": 0, "_right": 910, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 120, "_originalHeight": 0, "_id": "b3IBiDnTdE3JQfOdkJ6VX4"}, {"__type__": "cc.Node", "_name": "content", "_objFlags": 0, "_parent": {"__id__": 288}, "_children": [], "_active": true, "_components": [{"__id__": 294}, {"__id__": 295}, {"__id__": 296}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 141, "g": 97, "b": 98, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 800, "height": 250}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-115, -60, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 4, "groupIndex": 4, "_id": "d9mWxTfPtG7aSHkjGDQa5h"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 293}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_useOriginalSize": true, "_string": "泡泡里包裹着一管十分美味的能量，看上去还有多种口味选择，在果冻世界里很受欢迎！\n食用后可以瞬间恢复大量能量值。", "_N$string": "泡泡里包裹着一管十分美味的能量，看上去还有多种口味选择，在果冻世界里很受欢迎！\n食用后可以瞬间恢复大量能量值。", "_fontSize": 35, "_lineHeight": 50, "_enableWrapText": true, "_N$file": null, "_isSystemFontUsed": true, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 1, "_underlineHeight": 0, "_N$horizontalAlign": 0, "_N$verticalAlign": 0, "_N$fontFamily": "幼圆", "_N$overflow": 2, "_N$cacheMode": 0, "_id": "714Z7AhXRAALgK1+/trnhl"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 293}, "_enabled": true, "alignMode": 2, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 230, "_top": 120, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": "261hxhyshJZpdZRv2iT+JA"}, {"__type__": "5520aFIq0hD17P+ce6P30KM", "_name": "", "_objFlags": 0, "node": {"__id__": 293}, "_enabled": true, "num": 1, "_id": "d296LPIo1E0prkZbu2gYtL"}, {"__type__": "cc.Node", "_name": "energyProp", "_objFlags": 0, "_parent": {"__id__": 288}, "_children": [], "_active": true, "_components": [{"__id__": 298}, {"__id__": 299}, {"__id__": 300}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 252, "height": 252}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [414.2, 0, 0, 0, 0, 0, 1, 0.8, 0.8, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "dfxMJRTXRIDoZkNMosec8P"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 297}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "8cdb44ac-a3f6-449f-b354-7cd48cf84061"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": null, "_id": "9dS3euYi9EaaIlIbhpnUv3"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 297}, "_enabled": true, "alignMode": 2, "_target": null, "_alignFlags": 34, "_left": 729.4780000000001, "_right": 0, "_top": 0, "_bottom": 26.94400000000001, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": "92ndATiphPcYmFnoX09ZX2"}, {"__type__": "e0139aGBBFIBLYUi7ZvSe2q", "_name": "", "_objFlags": 0, "node": {"__id__": 297}, "_enabled": true, "_id": "b8CZ3hqpdGTqIUA6kcGS5M"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 288}, "_enabled": true, "alignMode": 2, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": "75fzyB4StPib7PXRk4oGzW"}, {"__type__": "cc.Node", "_name": "page9", "_objFlags": 0, "_parent": {"__id__": 171}, "_children": [{"__id__": 303}, {"__id__": 307}, {"__id__": 311}], "_active": false, "_components": [{"__id__": 315}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 1030, "height": 370}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 4, "groupIndex": 4, "_id": "01yVvOzW5C9arWGifpkXfA"}, {"__type__": "cc.Node", "_name": "title", "_objFlags": 0, "_parent": {"__id__": 302}, "_children": [], "_active": true, "_components": [{"__id__": 304}, {"__id__": 305}, {"__id__": 306}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 141, "g": 97, "b": 98, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 240, "height": 75.6}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-515, 147.2, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 4, "groupIndex": 4, "_id": "8cL1SvWD9JRL1HWIMkdSbR"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 303}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_useOriginalSize": true, "_string": "金币泡泡", "_N$string": "金币泡泡", "_fontSize": 60, "_lineHeight": 60, "_enableWrapText": true, "_N$file": null, "_isSystemFontUsed": true, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 0, "_N$verticalAlign": 1, "_N$fontFamily": "华文琥珀", "_N$overflow": 0, "_N$cacheMode": 0, "_id": "08QhwCaApDi75h/fH43HC4"}, {"__type__": "5520aFIq0hD17P+ce6P30KM", "_name": "", "_objFlags": 0, "node": {"__id__": 303}, "_enabled": true, "num": 0, "_id": "84t/cyQPxME6aJrmKIydpg"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 303}, "_enabled": true, "alignMode": 2, "_target": null, "_alignFlags": 9, "_left": 0, "_right": 910, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 120, "_originalHeight": 0, "_id": "6cRKv5oohFtrqKGhfr90WS"}, {"__type__": "cc.Node", "_name": "content", "_objFlags": 0, "_parent": {"__id__": 302}, "_children": [], "_active": true, "_components": [{"__id__": 308}, {"__id__": 309}, {"__id__": 310}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 141, "g": 97, "b": 98, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 800, "height": 250}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-115, -60, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 4, "groupIndex": 4, "_id": "8f4xmXHpFET6OSYO2AX+u7"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 307}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_useOriginalSize": true, "_string": "这个泡泡里有一枚用于收藏的果冻金币，拿在手上仿佛自己就是百万富翁！\n收集它可以获得大量分数！", "_N$string": "这个泡泡里有一枚用于收藏的果冻金币，拿在手上仿佛自己就是百万富翁！\n收集它可以获得大量分数！", "_fontSize": 35, "_lineHeight": 50, "_enableWrapText": true, "_N$file": null, "_isSystemFontUsed": true, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 1, "_underlineHeight": 0, "_N$horizontalAlign": 0, "_N$verticalAlign": 0, "_N$fontFamily": "幼圆", "_N$overflow": 2, "_N$cacheMode": 0, "_id": "44iCk6PahG9bz35x7LaqJg"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 307}, "_enabled": true, "alignMode": 2, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 230, "_top": 120, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": "a5cBVjzxNBaIjTFTZTWaWQ"}, {"__type__": "5520aFIq0hD17P+ce6P30KM", "_name": "", "_objFlags": 0, "node": {"__id__": 307}, "_enabled": true, "num": 1, "_id": "09KluWyilEPpARG8coVMDL"}, {"__type__": "cc.Node", "_name": "scoreProp", "_objFlags": 0, "_parent": {"__id__": 302}, "_children": [], "_active": true, "_components": [{"__id__": 312}, {"__id__": 313}, {"__id__": 314}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 252, "height": 252}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [414.2, 0, 0, 0, 0, 0, 1, 0.8, 0.8, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "9apeM5/NhD8YHL3QgciG+t"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 311}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "8cdb44ac-a3f6-449f-b354-7cd48cf84061"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": null, "_id": "b1lKCdzXFEYKS5d5rNesB2"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 311}, "_enabled": true, "alignMode": 2, "_target": null, "_alignFlags": 34, "_left": 729.4780000000001, "_right": 0, "_top": 0, "_bottom": 26.94400000000001, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": "73HCK4RcJHt4fOtlipZqq8"}, {"__type__": "e0139aGBBFIBLYUi7ZvSe2q", "_name": "", "_objFlags": 0, "node": {"__id__": 311}, "_enabled": true, "_id": "2cHNUVKfNN2J+AgVGvrCp7"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 302}, "_enabled": true, "alignMode": 2, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": "c6prFFQCRF3pUC6KH4CegH"}, {"__type__": "cc.Node", "_name": "page10", "_objFlags": 0, "_parent": {"__id__": 171}, "_children": [{"__id__": 317}, {"__id__": 321}, {"__id__": 325}], "_active": false, "_components": [{"__id__": 329}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 1030, "height": 370}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 4, "groupIndex": 4, "_id": "31FLsbCpZNyaN2KgI7DHnp"}, {"__type__": "cc.Node", "_name": "title", "_objFlags": 0, "_parent": {"__id__": 316}, "_children": [], "_active": true, "_components": [{"__id__": 318}, {"__id__": 319}, {"__id__": 320}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 141, "g": 97, "b": 98, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 240, "height": 75.6}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-515, 147.2, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 4, "groupIndex": 4, "_id": "a60MmiHb5LC7p1sy6U5mJU"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 317}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_useOriginalSize": true, "_string": "闪电泡泡", "_N$string": "闪电泡泡", "_fontSize": 60, "_lineHeight": 60, "_enableWrapText": true, "_N$file": null, "_isSystemFontUsed": true, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 0, "_N$verticalAlign": 1, "_N$fontFamily": "华文琥珀", "_N$overflow": 0, "_N$cacheMode": 0, "_id": "24acJNFbRDCauwt5Y2PiaX"}, {"__type__": "5520aFIq0hD17P+ce6P30KM", "_name": "", "_objFlags": 0, "node": {"__id__": 317}, "_enabled": true, "num": 0, "_id": "33dXV1KP1J1aW+KS0Hmvnw"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 317}, "_enabled": true, "alignMode": 2, "_target": null, "_alignFlags": 9, "_left": 0, "_right": 910, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 120, "_originalHeight": 0, "_id": "faTOUEislJSIoHqBvlCebA"}, {"__type__": "cc.Node", "_name": "content", "_objFlags": 0, "_parent": {"__id__": 316}, "_children": [], "_active": true, "_components": [{"__id__": 322}, {"__id__": 323}, {"__id__": 324}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 141, "g": 97, "b": 98, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 800, "height": 250}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-115, -60, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 4, "groupIndex": 4, "_id": "07FDfGNH1AgJaQVtKiMt3n"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 321}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_useOriginalSize": true, "_string": "一道自然形成的小闪电钻进了泡泡里，蕴藏着极大的能量！\n吃掉它可以瞬间降低平台以及泡泡的掉落速度！", "_N$string": "一道自然形成的小闪电钻进了泡泡里，蕴藏着极大的能量！\n吃掉它可以瞬间降低平台以及泡泡的掉落速度！", "_fontSize": 35, "_lineHeight": 50, "_enableWrapText": true, "_N$file": null, "_isSystemFontUsed": true, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 1, "_underlineHeight": 0, "_N$horizontalAlign": 0, "_N$verticalAlign": 0, "_N$fontFamily": "幼圆", "_N$overflow": 2, "_N$cacheMode": 0, "_id": "faR6YAQi9NE6ZpD3RuT5s7"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 321}, "_enabled": true, "alignMode": 2, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 230, "_top": 120, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": "f3xZcBBQpO2JBYWzblz0Wa"}, {"__type__": "5520aFIq0hD17P+ce6P30KM", "_name": "", "_objFlags": 0, "node": {"__id__": 321}, "_enabled": true, "num": 1, "_id": "34/WPxEeBFlo4yhbYCTDJH"}, {"__type__": "cc.Node", "_name": "mapUpProp", "_objFlags": 0, "_parent": {"__id__": 316}, "_children": [], "_active": true, "_components": [{"__id__": 326}, {"__id__": 327}, {"__id__": 328}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 252, "height": 252}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [414.2, 0, 0, 0, 0, 0, 1, 0.8, 0.8, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "ea83Z66RVPQaR4ysYhAjwy"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 325}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "8cdb44ac-a3f6-449f-b354-7cd48cf84061"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": null, "_id": "80Uh9HjDFHM7AyGpXF+1Xx"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 325}, "_enabled": true, "alignMode": 2, "_target": null, "_alignFlags": 34, "_left": 729.4780000000001, "_right": 0, "_top": 0, "_bottom": 26.94400000000001, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": "08/gi+db9L5pogZSw1ySvk"}, {"__type__": "e0139aGBBFIBLYUi7ZvSe2q", "_name": "", "_objFlags": 0, "node": {"__id__": 325}, "_enabled": true, "_id": "a9jATeESpII7z86yWXX0BG"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 316}, "_enabled": true, "alignMode": 2, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": "a0jXMxydZMUqkSnpfNz7QY"}, {"__type__": "cc.Node", "_name": "page11", "_objFlags": 0, "_parent": {"__id__": 171}, "_children": [{"__id__": 331}, {"__id__": 335}, {"__id__": 339}, {"__id__": 343}], "_active": false, "_components": [{"__id__": 347}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 1030, "height": 370}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 4, "groupIndex": 4, "_id": "fc9vRZrqdLwKORuUaMWmyj"}, {"__type__": "cc.Node", "_name": "title", "_objFlags": 0, "_parent": {"__id__": 330}, "_children": [], "_active": true, "_components": [{"__id__": 332}, {"__id__": 333}, {"__id__": 334}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 141, "g": 97, "b": 98, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 480, "height": 75.6}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-515, 147.2, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 4, "groupIndex": 4, "_id": "9c/q5rWoRCNpu7/pa3bAVv"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 331}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_useOriginalSize": true, "_string": "掉落的平台及泡泡", "_N$string": "掉落的平台及泡泡", "_fontSize": 60, "_lineHeight": 60, "_enableWrapText": true, "_N$file": null, "_isSystemFontUsed": true, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 0, "_N$verticalAlign": 1, "_N$fontFamily": "华文琥珀", "_N$overflow": 0, "_N$cacheMode": 0, "_id": "7bPBYPAzhDoLpDJPKmixca"}, {"__type__": "5520aFIq0hD17P+ce6P30KM", "_name": "", "_objFlags": 0, "node": {"__id__": 331}, "_enabled": true, "num": 0, "_id": "3bcuyUSqpPCb8rUtj9+Im0"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 331}, "_enabled": true, "alignMode": 2, "_target": null, "_alignFlags": 9, "_left": 0, "_right": 910, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 120, "_originalHeight": 0, "_id": "9bS6XmEGVBOpDfx6mB3WGw"}, {"__type__": "cc.Node", "_name": "content", "_objFlags": 0, "_parent": {"__id__": 330}, "_children": [], "_active": true, "_components": [{"__id__": 336}, {"__id__": 337}, {"__id__": 338}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 141, "g": 97, "b": 98, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 780, "height": 250}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-125, -60, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 4, "groupIndex": 4, "_id": "86VgdsBYNCdYonAKk0X2mz"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 335}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_useOriginalSize": true, "_string": "受到一股来自地面的奇怪引力，平台以及泡泡会一直往下掉落，而且掉落速度会越来越快！\n脆弱的泡泡在掉落过程中不幸碰到其他东西会破碎开来...\n而坚固的平台会摧毁掉落路径上的一切东西！", "_N$string": "受到一股来自地面的奇怪引力，平台以及泡泡会一直往下掉落，而且掉落速度会越来越快！\n脆弱的泡泡在掉落过程中不幸碰到其他东西会破碎开来...\n而坚固的平台会摧毁掉落路径上的一切东西！", "_fontSize": 35, "_lineHeight": 50, "_enableWrapText": true, "_N$file": null, "_isSystemFontUsed": true, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 1, "_underlineHeight": 0, "_N$horizontalAlign": 0, "_N$verticalAlign": 0, "_N$fontFamily": "幼圆", "_N$overflow": 2, "_N$cacheMode": 0, "_id": "dbB+oHKt5PyY3HBwNMiwmV"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 335}, "_enabled": true, "alignMode": 2, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 250, "_top": 120, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": "57dfCvAkJHgqqAhuqzDEtb"}, {"__type__": "5520aFIq0hD17P+ce6P30KM", "_name": "", "_objFlags": 0, "node": {"__id__": 335}, "_enabled": true, "num": 1, "_id": "f2But1oGVPVKO+ppxyuYRw"}, {"__type__": "cc.Node", "_name": "helpImg_CHPP", "_objFlags": 0, "_parent": {"__id__": 330}, "_children": [], "_active": true, "_components": [{"__id__": 340}, {"__id__": 341}, {"__id__": 342}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 763, "height": 298}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [362.4, 105.4, 0, 0, 0, 0, 1, 0.4, 0.4, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "1fDjZNwHZJyIXuTmP62BD3"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 339}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "8cdb44ac-a3f6-449f-b354-7cd48cf84061"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": null, "_id": "daM37bCmBIgqEfRywDMdnY"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 339}, "_enabled": true, "alignMode": 2, "_target": null, "_alignFlags": 33, "_left": 729.4780000000001, "_right": 0, "_top": 20, "_bottom": 26.94400000000001, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": "aez2QxV9ZG4JglHW4MlLk3"}, {"__type__": "e0139aGBBFIBLYUi7ZvSe2q", "_name": "", "_objFlags": 0, "node": {"__id__": 339}, "_enabled": true, "_id": "77a0NEufRARLiwdk5yXs0r"}, {"__type__": "cc.Node", "_name": "helpImg_CHXJ", "_objFlags": 0, "_parent": {"__id__": 330}, "_children": [], "_active": true, "_components": [{"__id__": 344}, {"__id__": 345}, {"__id__": 346}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 297, "height": 350}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [430.75, -47.5, 0, 0, 0, 0, 1, 0.5, 0.5, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "ef99pOO9JKKL0TeaYOMS8J"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 343}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "8cdb44ac-a3f6-449f-b354-7cd48cf84061"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": null, "_id": "23CIR2IbBNuLn4KLlcysu6"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 343}, "_enabled": true, "alignMode": 2, "_target": null, "_alignFlags": 36, "_left": 729.4780000000001, "_right": 10, "_top": 0, "_bottom": 50, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": "24SkfeSgVPeK7rS/DRWHVy"}, {"__type__": "e0139aGBBFIBLYUi7ZvSe2q", "_name": "", "_objFlags": 0, "node": {"__id__": 343}, "_enabled": true, "_id": "95YP8Ag0hLRJQfBAEv/jBc"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 330}, "_enabled": true, "alignMode": 2, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": "3f4vNdTcdPy6aiODLW+4MD"}, {"__type__": "cc.Node", "_name": "page12", "_objFlags": 0, "_parent": {"__id__": 171}, "_children": [{"__id__": 349}, {"__id__": 353}, {"__id__": 357}, {"__id__": 361}, {"__id__": 365}], "_active": false, "_components": [{"__id__": 369}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 1030, "height": 370}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 4, "groupIndex": 4, "_id": "2biJrfRHZLSLIud8gee+Mz"}, {"__type__": "cc.Node", "_name": "title", "_objFlags": 0, "_parent": {"__id__": 348}, "_children": [], "_active": true, "_components": [{"__id__": 350}, {"__id__": 351}, {"__id__": 352}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 141, "g": 97, "b": 98, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 420, "height": 75.6}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-515, 147.2, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 4, "groupIndex": 4, "_id": "67kyF0jz9EOZmSqm52loeT"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 349}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_useOriginalSize": true, "_string": "果冻世界的标志", "_N$string": "果冻世界的标志", "_fontSize": 60, "_lineHeight": 60, "_enableWrapText": true, "_N$file": null, "_isSystemFontUsed": true, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 0, "_N$verticalAlign": 1, "_N$fontFamily": "华文琥珀", "_N$overflow": 0, "_N$cacheMode": 0, "_id": "caUCGx4Z5IKLChhEKUzSBE"}, {"__type__": "5520aFIq0hD17P+ce6P30KM", "_name": "", "_objFlags": 0, "node": {"__id__": 349}, "_enabled": true, "num": 0, "_id": "b8cDJ2dzJO9LBg1YTIWWuD"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 349}, "_enabled": true, "alignMode": 2, "_target": null, "_alignFlags": 9, "_left": 0, "_right": 910, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 120, "_originalHeight": 0, "_id": "2d5XtwffZCkrvVACSqUdJh"}, {"__type__": "cc.Node", "_name": "content", "_objFlags": 0, "_parent": {"__id__": 348}, "_children": [], "_active": true, "_components": [{"__id__": 354}, {"__id__": 355}, {"__id__": 356}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 141, "g": 97, "b": 98, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 780, "height": 250}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-125, -60, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 4, "groupIndex": 4, "_id": "f7vm2U+llEdoAYg14UOm55"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 353}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_useOriginalSize": true, "_string": "金币标志代表着分数，在果冻存活的时间里会随着时间不停地增加！\n红色的箭头标志代表着果冻去过的最高高度！\n蓝色的箭头标志代表着果冻目前的高度！", "_N$string": "金币标志代表着分数，在果冻存活的时间里会随着时间不停地增加！\n红色的箭头标志代表着果冻去过的最高高度！\n蓝色的箭头标志代表着果冻目前的高度！", "_fontSize": 35, "_lineHeight": 50, "_enableWrapText": true, "_N$file": null, "_isSystemFontUsed": true, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 1, "_underlineHeight": 0, "_N$horizontalAlign": 0, "_N$verticalAlign": 0, "_N$fontFamily": "幼圆", "_N$overflow": 2, "_N$cacheMode": 0, "_id": "5e8KdG9edMb5ijwgVbro/C"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 353}, "_enabled": true, "alignMode": 2, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 250, "_top": 120, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": "30WWe81G5PjImsqERPNK5k"}, {"__type__": "5520aFIq0hD17P+ce6P30KM", "_name": "", "_objFlags": 0, "node": {"__id__": 353}, "_enabled": true, "num": 1, "_id": "647pg7UWpANowgbuoHBowD"}, {"__type__": "cc.Node", "_name": "scoreIcon", "_objFlags": 0, "_parent": {"__id__": 348}, "_children": [], "_active": true, "_components": [{"__id__": 358}, {"__id__": 359}, {"__id__": 360}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 91, "height": 91}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [399.5, 59.5, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "adG7J97YNH3p1kesVAg6wP"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 357}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "8cdb44ac-a3f6-449f-b354-7cd48cf84061"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": null, "_id": "86acnahI9GX4pinhl33cBG"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 357}, "_enabled": true, "alignMode": 2, "_target": null, "_alignFlags": 33, "_left": 0, "_right": 70, "_top": 80, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": "3f7tHV36hLLYiFOGst3L1S"}, {"__type__": "e0139aGBBFIBLYUi7ZvSe2q", "_name": "", "_objFlags": 0, "node": {"__id__": 357}, "_enabled": true, "_id": "de+w8UrxxPZ6JPeeIwf2eV"}, {"__type__": "cc.Node", "_name": "heightIcon", "_objFlags": 0, "_parent": {"__id__": 348}, "_children": [], "_active": true, "_components": [{"__id__": 362}, {"__id__": 363}, {"__id__": 364}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 91, "height": 91}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [319.5, -60.5, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "93dYFdooRD+7oyYY++p7yp"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 361}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "8cdb44ac-a3f6-449f-b354-7cd48cf84061"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": null, "_id": "84SaCtj+VBS4yjcE2EkAIa"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 361}, "_enabled": true, "alignMode": 2, "_target": null, "_alignFlags": 33, "_left": 0, "_right": 150, "_top": 200, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": "53fSjGUpFGLJSSQfhEmeXk"}, {"__type__": "e0139aGBBFIBLYUi7ZvSe2q", "_name": "", "_objFlags": 0, "node": {"__id__": 361}, "_enabled": true, "_id": "50kEGqFKBDgLa1B8nJhrOw"}, {"__type__": "cc.Node", "_name": "currentHeightIcon", "_objFlags": 0, "_parent": {"__id__": 348}, "_children": [], "_active": true, "_components": [{"__id__": 366}, {"__id__": 367}, {"__id__": 368}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 91, "height": 91}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [479.5, -60.5, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "61km5JQmpD9bS5ITTLhlje"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 365}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "8cdb44ac-a3f6-449f-b354-7cd48cf84061"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": null, "_id": "b2tWtVWttAkZCJbRh8xw58"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 365}, "_enabled": true, "alignMode": 2, "_target": null, "_alignFlags": 33, "_left": 0, "_right": -10, "_top": 200, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": "62R8u5PwRLK7AyiSG6fyvJ"}, {"__type__": "e0139aGBBFIBLYUi7ZvSe2q", "_name": "", "_objFlags": 0, "node": {"__id__": 365}, "_enabled": true, "_id": "eaXW3EcPNJapdZYKsgKegW"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 348}, "_enabled": true, "alignMode": 2, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": "2bYFVo6H5IwbwZh3fHXfYj"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 171}, "_enabled": true, "alignMode": 2, "_target": null, "_alignFlags": 45, "_left": 350, "_right": 100, "_top": 30, "_bottom": 100, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": "ackQCVtxRHObS/v4SocJTI"}, {"__type__": "cc.Node", "_name": "help<PERSON>elly", "_objFlags": 0, "_parent": {"__id__": 170}, "_children": [{"__id__": 372}], "_active": true, "_components": [{"__id__": 375}, {"__id__": 376}, {"__id__": 377}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 405, "height": 393}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-775.5, 353.7, 0, 0, 0, 0, 1, 1.8, 1.8, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 4, "groupIndex": 4, "_id": "2c9xktyJVHMqGDkkDykQ/j"}, {"__type__": "cc.Node", "_name": "jellyFace", "_objFlags": 0, "_parent": {"__id__": 371}, "_children": [], "_active": true, "_components": [{"__id__": 373}, {"__id__": 374}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 405, "height": 393}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 4, "groupIndex": 4, "_id": "29L90BHGRD8Yu5V3e2q+zQ"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 372}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "8cdb44ac-a3f6-449f-b354-7cd48cf84061"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": null, "_id": "564WdJNndLIpEfR0c0hSJd"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 372}, "_enabled": true, "alignMode": 2, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 40, "_originalHeight": 36, "_id": "f0Alq5NhtG26+C8lTsAadV"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 371}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "8cdb44ac-a3f6-449f-b354-7cd48cf84061"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "c5XuMLQPdOJaEyR/3WrZco"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 371}, "_enabled": true, "alignMode": 2, "_target": null, "_alignFlags": 12, "_left": -400, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": "ffiNIl9x9FX5IXZJ0uc86J"}, {"__type__": "e0139aGBBFIBLYUi7ZvSe2q", "_name": "", "_objFlags": 0, "node": {"__id__": 371}, "_enabled": true, "_id": "4fkG8zZnRDsr21rTd3VDU6"}, {"__type__": "cc.Node", "_name": "pageNum", "_objFlags": 0, "_parent": {"__id__": 170}, "_children": [{"__id__": 379}, {"__id__": 387}, {"__id__": 395}], "_active": true, "_components": [{"__id__": 399}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 200, "height": 52}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 1, "y": 0}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [640, 50, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 4, "groupIndex": 4, "_id": "50kc3nPPRG77f59apVWdy0"}, {"__type__": "cc.Node", "_name": "rightB<PERSON>on", "_objFlags": 0, "_parent": {"__id__": 378}, "_children": [{"__id__": 380}], "_active": true, "_components": [{"__id__": 384}, {"__id__": 386}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 45, "height": 52}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-22.5, 26, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "96nDta1K9F4afLyVUffbOz"}, {"__type__": "cc.Node", "_name": "rightButtonBg", "_objFlags": 512, "_parent": {"__id__": 379}, "_children": [], "_active": true, "_components": [{"__id__": 381}, {"__id__": 382}, {"__id__": 383}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 45, "height": 52}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "c83YLpcD5HYrU0ICUL669W"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 380}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "8cdb44ac-a3f6-449f-b354-7cd48cf84061"}, "_type": 1, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "aeis+errRKnahA1U9f0uRb"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 380}, "_enabled": true, "alignMode": 0, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 100, "_originalHeight": 40, "_id": "e81B1KDTJP1rYXxaPvIUJN"}, {"__type__": "e0139aGBBFIBLYUi7ZvSe2q", "_name": "", "_objFlags": 0, "node": {"__id__": 380}, "_enabled": true, "_id": "13O+tudLtHwaE2IpC6pYJz"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 379}, "_enabled": true, "_normalMaterial": {"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}, "_grayMaterial": null, "duration": 0.1, "zoomScale": 0.8, "clickEvents": [{"__id__": 385}], "_N$interactable": true, "_N$enableAutoGrayEffect": false, "_N$transition": 3, "transition": 3, "_N$normalColor": {"__type__": "cc.Color", "r": 230, "g": 230, "b": 230, "a": 255}, "_N$pressedColor": {"__type__": "cc.Color", "r": 200, "g": 200, "b": 200, "a": 255}, "pressedColor": {"__type__": "cc.Color", "r": 200, "g": 200, "b": 200, "a": 255}, "_N$hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$disabledColor": {"__type__": "cc.Color", "r": 120, "g": 120, "b": 120, "a": 200}, "_N$normalSprite": {"__uuid__": "f0048c10-f03e-4c97-b9d3-3506e1d58952"}, "_N$pressedSprite": {"__uuid__": "e9ec654c-97a2-4787-9325-e6a10375219a"}, "pressedSprite": {"__uuid__": "e9ec654c-97a2-4787-9325-e6a10375219a"}, "_N$hoverSprite": {"__uuid__": "f0048c10-f03e-4c97-b9d3-3506e1d58952"}, "hoverSprite": {"__uuid__": "f0048c10-f03e-4c97-b9d3-3506e1d58952"}, "_N$disabledSprite": {"__uuid__": "29158224-f8dd-4661-a796-1ffab537140e"}, "_N$target": {"__id__": 380}, "_id": "53T8I3Oj5CsrMrbZPkbpjY"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 164}, "component": "", "_componentId": "e30bfI+zYVKoqoyykABuGYL", "handler": "rightB<PERSON>on", "customEventData": ""}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 379}, "_enabled": true, "alignMode": 2, "_target": null, "_alignFlags": 34, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": "66QkSj0MdDLLqKzYOrAvZa"}, {"__type__": "cc.Node", "_name": "leftButton", "_objFlags": 0, "_parent": {"__id__": 378}, "_children": [{"__id__": 388}], "_active": true, "_components": [{"__id__": 392}, {"__id__": 394}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 45, "height": 52}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-177.5, 26, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "46LZKFjBJHmqd/iJFlMpsl"}, {"__type__": "cc.Node", "_name": "leftButtonBg", "_objFlags": 512, "_parent": {"__id__": 387}, "_children": [], "_active": true, "_components": [{"__id__": 389}, {"__id__": 390}, {"__id__": 391}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 45, "height": 52}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "aakktnXY5Ny77MmougKrRD"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 388}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "8cdb44ac-a3f6-449f-b354-7cd48cf84061"}, "_type": 1, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "45Ca1kv0RDuLhoWVswPKTs"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 388}, "_enabled": true, "alignMode": 0, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 100, "_originalHeight": 40, "_id": "50CEq2UHhNmaUlH5gJxkwE"}, {"__type__": "e0139aGBBFIBLYUi7ZvSe2q", "_name": "", "_objFlags": 0, "node": {"__id__": 388}, "_enabled": true, "_id": "f5O1wzioZAGKXH2y0t08Hh"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 387}, "_enabled": true, "_normalMaterial": {"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}, "_grayMaterial": null, "duration": 0.1, "zoomScale": 0.8, "clickEvents": [{"__id__": 393}], "_N$interactable": true, "_N$enableAutoGrayEffect": false, "_N$transition": 3, "transition": 3, "_N$normalColor": {"__type__": "cc.Color", "r": 230, "g": 230, "b": 230, "a": 255}, "_N$pressedColor": {"__type__": "cc.Color", "r": 200, "g": 200, "b": 200, "a": 255}, "pressedColor": {"__type__": "cc.Color", "r": 200, "g": 200, "b": 200, "a": 255}, "_N$hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$disabledColor": {"__type__": "cc.Color", "r": 120, "g": 120, "b": 120, "a": 200}, "_N$normalSprite": {"__uuid__": "f0048c10-f03e-4c97-b9d3-3506e1d58952"}, "_N$pressedSprite": {"__uuid__": "e9ec654c-97a2-4787-9325-e6a10375219a"}, "pressedSprite": {"__uuid__": "e9ec654c-97a2-4787-9325-e6a10375219a"}, "_N$hoverSprite": {"__uuid__": "f0048c10-f03e-4c97-b9d3-3506e1d58952"}, "hoverSprite": {"__uuid__": "f0048c10-f03e-4c97-b9d3-3506e1d58952"}, "_N$disabledSprite": {"__uuid__": "29158224-f8dd-4661-a796-1ffab537140e"}, "_N$target": {"__id__": 388}, "_id": "25OFZDdb1IkLUqQSKvC/AD"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 164}, "component": "", "_componentId": "e30bfI+zYVKoqoyykABuGYL", "handler": "leftButton", "customEventData": ""}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 387}, "_enabled": true, "alignMode": 2, "_target": null, "_alignFlags": 10, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": "8e5ENdQA9P5o/75VFzP819"}, {"__type__": "cc.Node", "_name": "numLabel", "_objFlags": 0, "_parent": {"__id__": 378}, "_children": [], "_active": true, "_components": [{"__id__": 396}, {"__id__": 397}, {"__id__": 398}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 141, "g": 97, "b": 98, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 80, "height": 52}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-100, 26, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "d2fmHs675LQ4KaKMEJ3zuR"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 395}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_useOriginalSize": false, "_string": "1", "_N$string": "1", "_fontSize": 52, "_lineHeight": 52, "_enableWrapText": true, "_N$file": null, "_isSystemFontUsed": true, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "华文琥珀", "_N$overflow": 2, "_N$cacheMode": 0, "_id": "85Tc26PhhH4pjwMAn8VRwh"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 395}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 18, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": "5cvMqGG1ZEvLsMrVcUUjyf"}, {"__type__": "5520aFIq0hD17P+ce6P30KM", "_name": "", "_objFlags": 0, "node": {"__id__": 395}, "_enabled": true, "num": 0, "_id": "572UgoagBNq5maBp6gyMBo"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 378}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 36, "_left": 740, "_right": 100, "_top": 250, "_bottom": 50, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": "1cLZtlwO9PxKej6cG4VFt8"}, {"__type__": "cc.Node", "_name": "helpCloseButton", "_objFlags": 0, "_parent": {"__id__": 170}, "_children": [{"__id__": 401}], "_active": true, "_components": [{"__id__": 405}, {"__id__": 407}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [690, 480, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "1e+tsVTltKhJ/W6+27EEam"}, {"__type__": "cc.Node", "_name": "helpCloseButtonBg", "_objFlags": 512, "_parent": {"__id__": 400}, "_children": [], "_active": true, "_components": [{"__id__": 402}, {"__id__": 403}, {"__id__": 404}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "f2WutyMg1CGK1YZrDufid/"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 401}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "8cdb44ac-a3f6-449f-b354-7cd48cf84061"}, "_type": 1, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "7a0gTVw/5LDLTmoazCgeIB"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 401}, "_enabled": true, "alignMode": 0, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 100, "_originalHeight": 40, "_id": "62UINWFkBMJ6Si14SAbcRU"}, {"__type__": "e0139aGBBFIBLYUi7ZvSe2q", "_name": "", "_objFlags": 0, "node": {"__id__": 401}, "_enabled": true, "_id": "b6OQ4KZ9BFWZbV/zmKXDRw"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 400}, "_enabled": true, "_normalMaterial": {"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}, "_grayMaterial": null, "duration": 0.1, "zoomScale": 0.8, "clickEvents": [{"__id__": 406}], "_N$interactable": true, "_N$enableAutoGrayEffect": false, "_N$transition": 3, "transition": 3, "_N$normalColor": {"__type__": "cc.Color", "r": 230, "g": 230, "b": 230, "a": 255}, "_N$pressedColor": {"__type__": "cc.Color", "r": 200, "g": 200, "b": 200, "a": 255}, "pressedColor": {"__type__": "cc.Color", "r": 200, "g": 200, "b": 200, "a": 255}, "_N$hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$disabledColor": {"__type__": "cc.Color", "r": 120, "g": 120, "b": 120, "a": 200}, "_N$normalSprite": {"__uuid__": "f0048c10-f03e-4c97-b9d3-3506e1d58952"}, "_N$pressedSprite": {"__uuid__": "e9ec654c-97a2-4787-9325-e6a10375219a"}, "pressedSprite": {"__uuid__": "e9ec654c-97a2-4787-9325-e6a10375219a"}, "_N$hoverSprite": {"__uuid__": "f0048c10-f03e-4c97-b9d3-3506e1d58952"}, "hoverSprite": {"__uuid__": "f0048c10-f03e-4c97-b9d3-3506e1d58952"}, "_N$disabledSprite": {"__uuid__": "29158224-f8dd-4661-a796-1ffab537140e"}, "_N$target": {"__id__": 401}, "_id": "219mFC82xD7JK7QA+Oz4gw"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 164}, "component": "", "_componentId": "e30bfI+zYVKoqoyykABuGYL", "handler": "closeButton", "customEventData": ""}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 400}, "_enabled": true, "alignMode": 2, "_target": null, "_alignFlags": 33, "_left": 0, "_right": 0, "_top": -30, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": "76Jvby9vFGm5/rjQIrCIfU"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 170}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "8cdb44ac-a3f6-449f-b354-7cd48cf84061"}, "_type": 1, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "72V/rVm2NEXZ6ovz9VwjZg"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 170}, "_enabled": true, "alignMode": 2, "_target": null, "_alignFlags": 44, "_left": 400, "_right": 20, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 362, "_originalHeight": 0, "_id": "4dIkXO6oJFXJpcEM9G84ed"}, {"__type__": "e0139aGBBFIBLYUi7ZvSe2q", "_name": "", "_objFlags": 0, "node": {"__id__": 170}, "_enabled": true, "_id": "32VZ3WLbNGk6vo36C2l5Zo"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 169}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 20, "_left": 1000.5, "_right": 1000.5, "_top": 562.5, "_bottom": -750, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": "4aAOm76hFPI6LscbR/aikv"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 164}, "_enabled": true, "alignMode": 2, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": "4ay2rZFApOAbp2UZlPt8ZN"}, {"__type__": "e30bfI+zYVKoqoyykABuGYL", "_name": "", "_objFlags": 0, "node": {"__id__": 164}, "_enabled": true, "content": {"__id__": 169}, "pageList": {"__id__": 171}, "pageNumLabel": {"__id__": 396}, "gameCanvas": null, "jellyFace": {"__id__": 373}, "_id": "23Dkg7TYJHVagpo6PeDZFg"}, {"__type__": "cc.<PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 4}, "_enabled": true, "_designResolution": {"__type__": "cc.Size", "width": 2001, "height": 1125}, "_fitWidth": false, "_fitHeight": true, "_id": "59Cd0ovbdF4byw5sbjJDx7"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 4}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": "29zXboiXFBKoIV4PQ2liTe"}, {"__type__": "ba56f/5++JEgofJmsneAzFZ", "_name": "", "_objFlags": 0, "node": {"__id__": 4}, "_enabled": true, "_id": "c7zGOi2y5CaaTkw255+Quo"}, {"__type__": "50cbeuVb8dIF6BjwsNUR42+", "_name": "", "_objFlags": 0, "node": {"__id__": 4}, "_enabled": true, "_id": "45m8lgYjxHwZ0M/B8+5N3s"}]