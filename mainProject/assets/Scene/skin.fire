[{"__type__": "cc.SceneAsset", "_name": "", "_objFlags": 0, "_native": "", "scene": {"__id__": 1}}, {"__type__": "cc.Scene", "_objFlags": 0, "_parent": null, "_children": [{"__id__": 2}], "_active": true, "_components": [], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_is3DNode": true, "_groupIndex": 0, "groupIndex": 0, "autoReleaseAssets": false, "_id": "12b74f3a-5acd-4388-9407-6590f7346994"}, {"__type__": "cc.Node", "_name": "<PERSON><PERSON>", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [{"__id__": 3}, {"__id__": 5}, {"__id__": 279}, {"__id__": 11}, {"__id__": 31}], "_active": true, "_components": [{"__id__": 290}, {"__id__": 291}, {"__id__": 292}, {"__id__": 293}], "_prefab": null, "_opacity": 0, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 2001, "height": 1125}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [1000.5, 562.5, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "a5esZu+45LA5mBpvttspPD"}, {"__type__": "cc.Node", "_name": "Main Camera", "_objFlags": 0, "_parent": {"__id__": 2}, "_children": [], "_active": true, "_components": [{"__id__": 4}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 960, "height": 640}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 251.46131716375783, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "e1WoFrQ79G7r4ZuQE3HlNb"}, {"__type__": "cc.Camera", "_name": "", "_objFlags": 0, "node": {"__id__": 3}, "_enabled": true, "_cullingMask": 4294967295, "_clearFlags": 7, "_backgroundColor": {"__type__": "cc.Color", "r": 249, "g": 209, "b": 162, "a": 255}, "_depth": -1, "_zoomRatio": 1, "_targetTexture": null, "_fov": 60, "_orthoSize": 10, "_nearClip": 1, "_farClip": 4096, "_ortho": true, "_rect": {"__type__": "cc.Rect", "x": 0, "y": 0, "width": 1, "height": 1}, "_renderStages": 1, "_alignWithScreen": true, "_id": "81GN3uXINKVLeW4+iKSlim"}, {"__type__": "cc.Node", "_name": "skinButtonFun", "_objFlags": 0, "_parent": {"__id__": 2}, "_children": [], "_active": true, "_components": [{"__id__": 6}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "a7/yrwDX9DdbLv5Nby04So"}, {"__type__": "13a82hWJGZLJYVWqsc9jyw9", "_name": "", "_objFlags": 0, "node": {"__id__": 5}, "_enabled": true, "unlockLabel": {"__id__": 7}, "content": {"__id__": 28}, "_id": "e0FWq8mYFIir3hz9jWqcI8"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 8}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_useOriginalSize": false, "_string": "解 锁", "_N$string": "解 锁", "_fontSize": 50, "_lineHeight": 60, "_enableWrapText": false, "_N$file": null, "_isSystemFontUsed": true, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "华文琥珀", "_N$overflow": 1, "_N$cacheMode": 1, "_id": "78vmdojt9Ol5WMAZQB3IFK"}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 512, "_parent": {"__id__": 9}, "_children": [], "_active": true, "_components": [{"__id__": 7}, {"__id__": 27}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 214, "g": 103, "b": 72, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 200, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "bfUarSBbZGnpZ0sc5gzCxX"}, {"__type__": "cc.Node", "_name": "textButtonBg", "_objFlags": 512, "_parent": {"__id__": 10}, "_children": [{"__id__": 8}], "_active": true, "_components": [{"__id__": 24}, {"__id__": 25}, {"__id__": 26}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 200, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "a098LJtadIDbknwGhsfbWZ"}, {"__type__": "cc.Node", "_name": "unlockButton", "_objFlags": 0, "_parent": {"__id__": 11}, "_children": [{"__id__": 9}], "_active": true, "_components": [{"__id__": 21}, {"__id__": 23}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 200, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -482.5, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "98L24+JbhA3I/vptg76YCb"}, {"__type__": "cc.Node", "_name": "ui", "_objFlags": 0, "_parent": {"__id__": 2}, "_children": [{"__id__": 12}, {"__id__": 10}], "_active": true, "_components": [{"__id__": 20}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 2001, "height": 1125}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "1bT+W2oTFE8aRgYuFDJ5C5"}, {"__type__": "cc.Node", "_name": "backButton", "_objFlags": 0, "_parent": {"__id__": 11}, "_children": [{"__id__": 13}], "_active": true, "_components": [{"__id__": 17}, {"__id__": 19}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-890.5, 482.5, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "dddUFcnXJFxbBv02DVxe9M"}, {"__type__": "cc.Node", "_name": "backButtonBg", "_objFlags": 512, "_parent": {"__id__": 12}, "_children": [], "_active": true, "_components": [{"__id__": 14}, {"__id__": 15}, {"__id__": 16}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "08lMD0UbpJSq6sbI+P9wpJ"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 13}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "8cdb44ac-a3f6-449f-b354-7cd48cf84061"}, "_type": 1, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "d49O/fx0xByq/OsDZaDGNu"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 13}, "_enabled": true, "alignMode": 0, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 100, "_originalHeight": 40, "_id": "edURklJCtJT59XU+erzHGg"}, {"__type__": "e0139aGBBFIBLYUi7ZvSe2q", "_name": "", "_objFlags": 0, "node": {"__id__": 13}, "_enabled": true, "_id": "84qoqK1RhAiZXNr9TPcXmY"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 12}, "_enabled": true, "_normalMaterial": {"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}, "_grayMaterial": null, "duration": 0.1, "zoomScale": 1.2, "clickEvents": [{"__id__": 18}], "_N$interactable": true, "_N$enableAutoGrayEffect": false, "_N$transition": 2, "transition": 2, "_N$normalColor": {"__type__": "cc.Color", "r": 230, "g": 230, "b": 230, "a": 255}, "_N$pressedColor": {"__type__": "cc.Color", "r": 200, "g": 200, "b": 200, "a": 255}, "pressedColor": {"__type__": "cc.Color", "r": 200, "g": 200, "b": 200, "a": 255}, "_N$hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$disabledColor": {"__type__": "cc.Color", "r": 120, "g": 120, "b": 120, "a": 200}, "_N$normalSprite": {"__uuid__": "8cdb44ac-a3f6-449f-b354-7cd48cf84061"}, "_N$pressedSprite": null, "pressedSprite": null, "_N$hoverSprite": null, "hoverSprite": null, "_N$disabledSprite": null, "_N$target": {"__id__": 13}, "_id": "1evOgqY/FCIqeda+1AdbZS"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 5}, "component": "", "_componentId": "13a82hWJGZLJYVWqsc9jyw9", "handler": "backButton", "customEventData": ""}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 12}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 9, "_left": 60, "_right": 0, "_top": 30, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": "6fIZGJdwxA8I3wmY+di4z3"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 11}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": "14Fn3nqPVEj5aWMnrOBesu"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 10}, "_enabled": true, "_normalMaterial": {"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}, "_grayMaterial": null, "duration": 0.1, "zoomScale": 1.2, "clickEvents": [{"__id__": 22}], "_N$interactable": true, "_N$enableAutoGrayEffect": false, "_N$transition": 2, "transition": 2, "_N$normalColor": {"__type__": "cc.Color", "r": 230, "g": 230, "b": 230, "a": 255}, "_N$pressedColor": {"__type__": "cc.Color", "r": 200, "g": 200, "b": 200, "a": 255}, "pressedColor": {"__type__": "cc.Color", "r": 200, "g": 200, "b": 200, "a": 255}, "_N$hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$disabledColor": {"__type__": "cc.Color", "r": 120, "g": 120, "b": 120, "a": 200}, "_N$normalSprite": {"__uuid__": "8cdb44ac-a3f6-449f-b354-7cd48cf84061"}, "_N$pressedSprite": null, "pressedSprite": null, "_N$hoverSprite": null, "hoverSprite": null, "_N$disabledSprite": null, "_N$target": {"__id__": 9}, "_id": "0fDWdd5pFK7Z07EhrKTtMD"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 5}, "component": "", "_componentId": "13a82hWJGZLJYVWqsc9jyw9", "handler": "unlockButton", "customEventData": ""}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 10}, "_enabled": true, "alignMode": 2, "_target": null, "_alignFlags": 20, "_left": 0, "_right": 0, "_top": 0, "_bottom": 30, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": "a1qPd91NZMoY75Xy9x0bmC"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 9}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "8cdb44ac-a3f6-449f-b354-7cd48cf84061"}, "_type": 1, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "53GJpkhApGa7CTdCSCyRot"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 9}, "_enabled": true, "alignMode": 0, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 100, "_originalHeight": 40, "_id": "5bpGz8am1IioWoSuFq0szm"}, {"__type__": "e0139aGBBFIBLYUi7ZvSe2q", "_name": "", "_objFlags": 0, "node": {"__id__": 9}, "_enabled": true, "_id": "50s/ej+TtOkqk+jNv3z+OK"}, {"__type__": "5520aFIq0hD17P+ce6P30KM", "_name": "", "_objFlags": 0, "node": {"__id__": 8}, "_enabled": true, "_id": "a8wWb0w7hC97EJ5LqIiO3M"}, {"__type__": "cc.Node", "_name": "content", "_objFlags": 512, "_parent": {"__id__": 29}, "_children": [{"__id__": 134}, {"__id__": 158}, {"__id__": 182}, {"__id__": 206}, {"__id__": 230}, {"__id__": 254}], "_active": true, "_components": [{"__id__": 131}, {"__id__": 278}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 3400, "height": 400}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "65v9w5sGdM174ueN8L18bd"}, {"__type__": "cc.Node", "_name": "view", "_objFlags": 512, "_parent": {"__id__": 30}, "_children": [{"__id__": 28}], "_active": true, "_components": [{"__id__": 132}, {"__id__": 133}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 2001, "height": 805}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "b4ViT9OsZAM7pkvL/NKjLZ"}, {"__type__": "cc.Node", "_name": "fourBoxList", "_objFlags": 0, "_parent": {"__id__": 31}, "_children": [{"__id__": 29}], "_active": true, "_components": [{"__id__": 126}, {"__id__": 127}, {"__id__": 129}, {"__id__": 130}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 2001, "height": 805}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "74d+Imi/BIl4WYv/MMIBe2"}, {"__type__": "cc.Node", "_name": "skinContent", "_objFlags": 0, "_parent": {"__id__": 2}, "_children": [{"__id__": 30}, {"__id__": 32}], "_active": true, "_components": [{"__id__": 125}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 2001, "height": 805}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "bcak4sKrZD3rmbjlr9pbUa"}, {"__type__": "cc.Node", "_name": "sideButton", "_objFlags": 0, "_parent": {"__id__": 31}, "_children": [{"__id__": 33}, {"__id__": 48}, {"__id__": 63}, {"__id__": 78}, {"__id__": 93}, {"__id__": 108}], "_active": true, "_components": [{"__id__": 123}, {"__id__": 124}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 300, "height": 725}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-1030.5, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "36ZQGpsA5D96vyry6Pz2FI"}, {"__type__": "cc.Node", "_name": "blueButton", "_objFlags": 0, "_parent": {"__id__": 32}, "_children": [{"__id__": 34}, {"__id__": 38}, {"__id__": 42}], "_active": true, "_components": [{"__id__": 45}, {"__id__": 47}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 300, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [120, 312.5, 0, 0, 0, 0, 1, 0.8, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "04IqQCGS1KHpKvlGbNy9UB"}, {"__type__": "cc.Node", "_name": "blueButtonBg", "_objFlags": 512, "_parent": {"__id__": 33}, "_children": [], "_active": true, "_components": [{"__id__": 35}, {"__id__": 36}, {"__id__": 37}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 300, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "0fT6xME3NE7ZSAZfa6ned5"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 34}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "8cdb44ac-a3f6-449f-b354-7cd48cf84061"}, "_type": 1, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "1f8Rm2jidN/YxYHoasFQpR"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 34}, "_enabled": true, "alignMode": 0, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 100, "_originalHeight": 40, "_id": "30bEUiWL5AIrEu8pApTnSN"}, {"__type__": "e0139aGBBFIBLYUi7ZvSe2q", "_name": "", "_objFlags": 0, "node": {"__id__": 34}, "_enabled": true, "_id": "aaiZeyyDNH/r+i35R1rojW"}, {"__type__": "cc.Node", "_name": "buttonShadow", "_objFlags": 0, "_parent": {"__id__": 33}, "_children": [], "_active": true, "_components": [{"__id__": 39}, {"__id__": 40}, {"__id__": 41}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 334, "height": 128}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -8, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "62Mt5GnA5MGIk1bXijqcrk"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 38}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": null, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": null, "_id": "95kS4I/KdFYb5aqmUpUjuJ"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 38}, "_enabled": true, "alignMode": 2, "_target": null, "_alignFlags": 45, "_left": -17, "_right": -17, "_top": -6, "_bottom": -22, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 278, "_originalHeight": 128, "_id": "33KpeYxkVHC589h+Sa12U4"}, {"__type__": "e0139aGBBFIBLYUi7ZvSe2q", "_name": "", "_objFlags": 0, "node": {"__id__": 38}, "_enabled": true, "_id": "c1bEk0iyxOxLxWiMQeQLxU"}, {"__type__": "cc.Node", "_name": "lock", "_objFlags": 0, "_parent": {"__id__": 33}, "_children": [], "_active": true, "_components": [{"__id__": 43}, {"__id__": 44}], "_prefab": null, "_opacity": 0, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 35, "height": 40}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "c3P9uWtw5DtL3kN7j2GdRU"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 42}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": null, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "12eE/DGwxLq45KJJNOb4C6"}, {"__type__": "e0139aGBBFIBLYUi7ZvSe2q", "_name": "", "_objFlags": 0, "node": {"__id__": 42}, "_enabled": true, "_id": "ee+OwJUF1BbpuZ0Y2zRSrs"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 33}, "_enabled": true, "_normalMaterial": {"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}, "_grayMaterial": null, "duration": 0.1, "zoomScale": 1.2, "clickEvents": [{"__id__": 46}], "_N$interactable": true, "_N$enableAutoGrayEffect": false, "_N$transition": 1, "transition": 1, "_N$normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$pressedColor": {"__type__": "cc.Color", "r": 150, "g": 150, "b": 150, "a": 255}, "pressedColor": {"__type__": "cc.Color", "r": 150, "g": 150, "b": 150, "a": 255}, "_N$hoverColor": {"__type__": "cc.Color", "r": 200, "g": 200, "b": 200, "a": 255}, "hoverColor": {"__type__": "cc.Color", "r": 200, "g": 200, "b": 200, "a": 255}, "_N$disabledColor": {"__type__": "cc.Color", "r": 120, "g": 120, "b": 120, "a": 200}, "_N$normalSprite": null, "_N$pressedSprite": {"__uuid__": "e9ec654c-97a2-4787-9325-e6a10375219a"}, "pressedSprite": {"__uuid__": "e9ec654c-97a2-4787-9325-e6a10375219a"}, "_N$hoverSprite": {"__uuid__": "f0048c10-f03e-4c97-b9d3-3506e1d58952"}, "hoverSprite": {"__uuid__": "f0048c10-f03e-4c97-b9d3-3506e1d58952"}, "_N$disabledSprite": {"__uuid__": "29158224-f8dd-4661-a796-1ffab537140e"}, "_N$target": {"__id__": 34}, "_id": "6bgaU6HttHLZLkTSAmdS+x"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 30}, "component": "", "_componentId": "614e6IrDfpOdbOzK09O8Qzm", "handler": "clickSideButton", "customEventData": "0"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 33}, "_enabled": true, "alignMode": 2, "_target": null, "_alignFlags": 8, "_left": 0, "_right": 200, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 100, "_originalHeight": 0, "_id": "7baEboca9FeIgfVGZAYicD"}, {"__type__": "cc.Node", "_name": "appleButton", "_objFlags": 0, "_parent": {"__id__": 32}, "_children": [{"__id__": 49}, {"__id__": 53}, {"__id__": 57}], "_active": true, "_components": [{"__id__": 60}, {"__id__": 62}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 300, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [120, 187.5, 0, 0, 0, 0, 1, 0.8, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "944aF7C6FO3IQkT5P6pSE/"}, {"__type__": "cc.Node", "_name": "appleButtonBg", "_objFlags": 512, "_parent": {"__id__": 48}, "_children": [], "_active": true, "_components": [{"__id__": 50}, {"__id__": 51}, {"__id__": 52}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 300, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "a1In758QND36uP/dsZydcP"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 49}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "8cdb44ac-a3f6-449f-b354-7cd48cf84061"}, "_type": 1, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "36vb9vVrdJI7mVZRG/Odkp"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 49}, "_enabled": true, "alignMode": 0, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 100, "_originalHeight": 40, "_id": "14f6uPpQdFw7aCHkr7SDCM"}, {"__type__": "e0139aGBBFIBLYUi7ZvSe2q", "_name": "", "_objFlags": 0, "node": {"__id__": 49}, "_enabled": true, "_id": "87sWtcGB9LgbTTcjfVIOx5"}, {"__type__": "cc.Node", "_name": "buttonShadow", "_objFlags": 0, "_parent": {"__id__": 48}, "_children": [], "_active": true, "_components": [{"__id__": 54}, {"__id__": 55}, {"__id__": 56}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 334, "height": 128}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -8, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "12Ygnp0sRLjrrY9KH7fNf3"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 53}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": null, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": null, "_id": "eeQTl7BOBAXJV3tYqIHuJr"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 53}, "_enabled": true, "alignMode": 2, "_target": null, "_alignFlags": 45, "_left": -17, "_right": -17, "_top": -6, "_bottom": -22, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 278, "_originalHeight": 128, "_id": "c3S1FP18xNypUEWQlJnH73"}, {"__type__": "e0139aGBBFIBLYUi7ZvSe2q", "_name": "", "_objFlags": 0, "node": {"__id__": 53}, "_enabled": true, "_id": "aa4eOwvCxF5aviY1Np8ExH"}, {"__type__": "cc.Node", "_name": "lock", "_objFlags": 0, "_parent": {"__id__": 48}, "_children": [], "_active": true, "_components": [{"__id__": 58}, {"__id__": 59}], "_prefab": null, "_opacity": 0, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 35, "height": 40}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "3cbGOZ99dMLYI+8YUjZqmk"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 57}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": null, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "4bH4MRjUxGkYoyMeV8e0Lw"}, {"__type__": "e0139aGBBFIBLYUi7ZvSe2q", "_name": "", "_objFlags": 0, "node": {"__id__": 57}, "_enabled": true, "_id": "3bh/M1QqpLqIhi1nRe6m7V"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 48}, "_enabled": true, "_normalMaterial": {"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}, "_grayMaterial": null, "duration": 0.1, "zoomScale": 1.2, "clickEvents": [{"__id__": 61}], "_N$interactable": true, "_N$enableAutoGrayEffect": false, "_N$transition": 1, "transition": 1, "_N$normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$pressedColor": {"__type__": "cc.Color", "r": 150, "g": 150, "b": 150, "a": 255}, "pressedColor": {"__type__": "cc.Color", "r": 150, "g": 150, "b": 150, "a": 255}, "_N$hoverColor": {"__type__": "cc.Color", "r": 200, "g": 200, "b": 200, "a": 255}, "hoverColor": {"__type__": "cc.Color", "r": 200, "g": 200, "b": 200, "a": 255}, "_N$disabledColor": {"__type__": "cc.Color", "r": 120, "g": 120, "b": 120, "a": 200}, "_N$normalSprite": null, "_N$pressedSprite": {"__uuid__": "e9ec654c-97a2-4787-9325-e6a10375219a"}, "pressedSprite": {"__uuid__": "e9ec654c-97a2-4787-9325-e6a10375219a"}, "_N$hoverSprite": {"__uuid__": "f0048c10-f03e-4c97-b9d3-3506e1d58952"}, "hoverSprite": {"__uuid__": "f0048c10-f03e-4c97-b9d3-3506e1d58952"}, "_N$disabledSprite": {"__uuid__": "29158224-f8dd-4661-a796-1ffab537140e"}, "_N$target": {"__id__": 49}, "_id": "02XnYyR5ZBa7x0itlSZ7Hf"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 30}, "component": "", "_componentId": "614e6IrDfpOdbOzK09O8Qzm", "handler": "clickSideButton", "customEventData": "1"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 48}, "_enabled": true, "alignMode": 2, "_target": null, "_alignFlags": 8, "_left": 0, "_right": 100, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 100, "_originalHeight": 0, "_id": "23BZq0YqxMB7Wv/SalPoLC"}, {"__type__": "cc.Node", "_name": "orangeButton", "_objFlags": 0, "_parent": {"__id__": 32}, "_children": [{"__id__": 64}, {"__id__": 68}, {"__id__": 72}], "_active": true, "_components": [{"__id__": 75}, {"__id__": 77}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 300, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [120, 62.5, 0, 0, 0, 0, 1, 0.8, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "b6A8slMWdGB6++sqy4zT7L"}, {"__type__": "cc.Node", "_name": "orangeButtonBg", "_objFlags": 512, "_parent": {"__id__": 63}, "_children": [], "_active": true, "_components": [{"__id__": 65}, {"__id__": 66}, {"__id__": 67}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 300, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "d6SSYMcopDAIHhpxuf4zhN"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 64}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "8cdb44ac-a3f6-449f-b354-7cd48cf84061"}, "_type": 1, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "a9DxlrendK350sHLLGGj7f"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 64}, "_enabled": true, "alignMode": 0, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 100, "_originalHeight": 40, "_id": "c3CRfXaDZFpIuqv5bU1bo1"}, {"__type__": "e0139aGBBFIBLYUi7ZvSe2q", "_name": "", "_objFlags": 0, "node": {"__id__": 64}, "_enabled": true, "_id": "c14pMbxTlKrbckP366LHu4"}, {"__type__": "cc.Node", "_name": "buttonShadow", "_objFlags": 0, "_parent": {"__id__": 63}, "_children": [], "_active": true, "_components": [{"__id__": 69}, {"__id__": 70}, {"__id__": 71}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 334, "height": 128}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -8, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "c3jV+uahpIpohp6+TGzJD6"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 68}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": null, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": null, "_id": "caRHMGQY9L7af9u6n+qGvF"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 68}, "_enabled": true, "alignMode": 2, "_target": null, "_alignFlags": 45, "_left": -17, "_right": -17, "_top": -6, "_bottom": -22, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 278, "_originalHeight": 128, "_id": "57V0nBKwlGW4U/6zYCWvP2"}, {"__type__": "e0139aGBBFIBLYUi7ZvSe2q", "_name": "", "_objFlags": 0, "node": {"__id__": 68}, "_enabled": true, "_id": "3fccEvo2xG448GyT+CqUJb"}, {"__type__": "cc.Node", "_name": "lock", "_objFlags": 0, "_parent": {"__id__": 63}, "_children": [], "_active": true, "_components": [{"__id__": 73}, {"__id__": 74}], "_prefab": null, "_opacity": 0, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 35, "height": 40}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "85gdc9EhFOsIaVn/XYnyMM"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 72}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": null, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "93N7skl1JHfowTD02pSfD/"}, {"__type__": "e0139aGBBFIBLYUi7ZvSe2q", "_name": "", "_objFlags": 0, "node": {"__id__": 72}, "_enabled": true, "_id": "c2O2gNgE5M4KTTN1w3oX8B"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 63}, "_enabled": true, "_normalMaterial": {"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}, "_grayMaterial": null, "duration": 0.1, "zoomScale": 1.2, "clickEvents": [{"__id__": 76}], "_N$interactable": true, "_N$enableAutoGrayEffect": false, "_N$transition": 1, "transition": 1, "_N$normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$pressedColor": {"__type__": "cc.Color", "r": 150, "g": 150, "b": 150, "a": 255}, "pressedColor": {"__type__": "cc.Color", "r": 150, "g": 150, "b": 150, "a": 255}, "_N$hoverColor": {"__type__": "cc.Color", "r": 200, "g": 200, "b": 200, "a": 255}, "hoverColor": {"__type__": "cc.Color", "r": 200, "g": 200, "b": 200, "a": 255}, "_N$disabledColor": {"__type__": "cc.Color", "r": 120, "g": 120, "b": 120, "a": 200}, "_N$normalSprite": null, "_N$pressedSprite": {"__uuid__": "e9ec654c-97a2-4787-9325-e6a10375219a"}, "pressedSprite": {"__uuid__": "e9ec654c-97a2-4787-9325-e6a10375219a"}, "_N$hoverSprite": {"__uuid__": "f0048c10-f03e-4c97-b9d3-3506e1d58952"}, "hoverSprite": {"__uuid__": "f0048c10-f03e-4c97-b9d3-3506e1d58952"}, "_N$disabledSprite": {"__uuid__": "29158224-f8dd-4661-a796-1ffab537140e"}, "_N$target": {"__id__": 64}, "_id": "a6DIIqtKVJ1JNvOdyf6DC6"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 30}, "component": "", "_componentId": "614e6IrDfpOdbOzK09O8Qzm", "handler": "clickSideButton", "customEventData": "2"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 63}, "_enabled": true, "alignMode": 2, "_target": null, "_alignFlags": 8, "_left": 0, "_right": 100, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 100, "_originalHeight": 0, "_id": "25E2L7zAZO05VwE4q53oMv"}, {"__type__": "cc.Node", "_name": "melonButton", "_objFlags": 0, "_parent": {"__id__": 32}, "_children": [{"__id__": 79}, {"__id__": 83}, {"__id__": 87}], "_active": true, "_components": [{"__id__": 90}, {"__id__": 92}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 300, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [120, -62.5, 0, 0, 0, 0, 1, 0.8, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "e40PqvlvhPCoYom7nuyEOp"}, {"__type__": "cc.Node", "_name": "melonButtonBg", "_objFlags": 512, "_parent": {"__id__": 78}, "_children": [], "_active": true, "_components": [{"__id__": 80}, {"__id__": 81}, {"__id__": 82}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 300, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "9bVMoLNiJFv7umlPdnl54w"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 79}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "8cdb44ac-a3f6-449f-b354-7cd48cf84061"}, "_type": 1, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "56uyc8ZZJDCIl31RDVfYcS"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 79}, "_enabled": true, "alignMode": 0, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 100, "_originalHeight": 40, "_id": "ddMJrN6YBLabpEqRGL0VZg"}, {"__type__": "e0139aGBBFIBLYUi7ZvSe2q", "_name": "", "_objFlags": 0, "node": {"__id__": 79}, "_enabled": true, "_id": "22W2QzVWRAjrDjRbMEbVIM"}, {"__type__": "cc.Node", "_name": "buttonShadow", "_objFlags": 0, "_parent": {"__id__": 78}, "_children": [], "_active": true, "_components": [{"__id__": 84}, {"__id__": 85}, {"__id__": 86}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 334, "height": 128}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -8, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "fbKnk3euxO0ok0axFq9zns"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 83}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": null, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": null, "_id": "69OB0JPZBLn4FUSuiKfVoE"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 83}, "_enabled": true, "alignMode": 2, "_target": null, "_alignFlags": 45, "_left": -17, "_right": -17, "_top": -6, "_bottom": -22, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 278, "_originalHeight": 128, "_id": "d79qtOWS5MM5vmOUAXDCKb"}, {"__type__": "e0139aGBBFIBLYUi7ZvSe2q", "_name": "", "_objFlags": 0, "node": {"__id__": 83}, "_enabled": true, "_id": "86HJlZao9KAI+RCjAuITS5"}, {"__type__": "cc.Node", "_name": "lock", "_objFlags": 0, "_parent": {"__id__": 78}, "_children": [], "_active": true, "_components": [{"__id__": 88}, {"__id__": 89}], "_prefab": null, "_opacity": 0, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 35, "height": 40}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "dcabG0wftDYaNX1mngO0Pi"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 87}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": null, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "c1TC1aVIVKXZ9kHZItNgjf"}, {"__type__": "e0139aGBBFIBLYUi7ZvSe2q", "_name": "", "_objFlags": 0, "node": {"__id__": 87}, "_enabled": true, "_id": "adxe2sM3tDTK8+BAjFsOBa"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 78}, "_enabled": true, "_normalMaterial": {"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}, "_grayMaterial": null, "duration": 0.1, "zoomScale": 1.2, "clickEvents": [{"__id__": 91}], "_N$interactable": true, "_N$enableAutoGrayEffect": false, "_N$transition": 1, "transition": 1, "_N$normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$pressedColor": {"__type__": "cc.Color", "r": 150, "g": 150, "b": 150, "a": 255}, "pressedColor": {"__type__": "cc.Color", "r": 150, "g": 150, "b": 150, "a": 255}, "_N$hoverColor": {"__type__": "cc.Color", "r": 200, "g": 200, "b": 200, "a": 255}, "hoverColor": {"__type__": "cc.Color", "r": 200, "g": 200, "b": 200, "a": 255}, "_N$disabledColor": {"__type__": "cc.Color", "r": 120, "g": 120, "b": 120, "a": 200}, "_N$normalSprite": null, "_N$pressedSprite": {"__uuid__": "e9ec654c-97a2-4787-9325-e6a10375219a"}, "pressedSprite": {"__uuid__": "e9ec654c-97a2-4787-9325-e6a10375219a"}, "_N$hoverSprite": {"__uuid__": "f0048c10-f03e-4c97-b9d3-3506e1d58952"}, "hoverSprite": {"__uuid__": "f0048c10-f03e-4c97-b9d3-3506e1d58952"}, "_N$disabledSprite": {"__uuid__": "29158224-f8dd-4661-a796-1ffab537140e"}, "_N$target": {"__id__": 79}, "_id": "c7NMeaYe9Aj52UAPO1pOED"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 30}, "component": "", "_componentId": "614e6IrDfpOdbOzK09O8Qzm", "handler": "clickSideButton", "customEventData": "3"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 78}, "_enabled": true, "alignMode": 2, "_target": null, "_alignFlags": 8, "_left": 0, "_right": 100, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 100, "_originalHeight": 0, "_id": "81CMhFH59NBK9Sg36YYrnB"}, {"__type__": "cc.Node", "_name": "berriesButton", "_objFlags": 0, "_parent": {"__id__": 32}, "_children": [{"__id__": 94}, {"__id__": 98}, {"__id__": 102}], "_active": true, "_components": [{"__id__": 105}, {"__id__": 107}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 300, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [120, -187.5, 0, 0, 0, 0, 1, 0.8, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "86ugeiu51Jko/JPc3Elucr"}, {"__type__": "cc.Node", "_name": "berriesButtonBg", "_objFlags": 512, "_parent": {"__id__": 93}, "_children": [], "_active": true, "_components": [{"__id__": 95}, {"__id__": 96}, {"__id__": 97}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 300, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "80x/B5l2dFmJEOZJwWf0JD"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 94}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "8cdb44ac-a3f6-449f-b354-7cd48cf84061"}, "_type": 1, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "ffHWyDKK1BQZbL02Az+bPD"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 94}, "_enabled": true, "alignMode": 0, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 100, "_originalHeight": 40, "_id": "fazIELb8BDOozdMl2gf5Ep"}, {"__type__": "e0139aGBBFIBLYUi7ZvSe2q", "_name": "", "_objFlags": 0, "node": {"__id__": 94}, "_enabled": true, "_id": "e5sG0/F/RPuqhuumU5BsVO"}, {"__type__": "cc.Node", "_name": "buttonShadow", "_objFlags": 0, "_parent": {"__id__": 93}, "_children": [], "_active": true, "_components": [{"__id__": 99}, {"__id__": 100}, {"__id__": 101}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 334, "height": 128}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -8, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "471aIaabpDObXrWhfu1fr4"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 98}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": null, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": null, "_id": "72rJBP0UdIFqK4kLRrcwNA"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 98}, "_enabled": true, "alignMode": 2, "_target": null, "_alignFlags": 45, "_left": -17, "_right": -17, "_top": -6, "_bottom": -22, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 278, "_originalHeight": 128, "_id": "9bSPVK0PBH4LNArvGEiFTw"}, {"__type__": "e0139aGBBFIBLYUi7ZvSe2q", "_name": "", "_objFlags": 0, "node": {"__id__": 98}, "_enabled": true, "_id": "e25K+CP7dOa6Zis5LWEuYx"}, {"__type__": "cc.Node", "_name": "lock", "_objFlags": 0, "_parent": {"__id__": 93}, "_children": [], "_active": true, "_components": [{"__id__": 103}, {"__id__": 104}], "_prefab": null, "_opacity": 0, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 35, "height": 40}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "8ePZ4KogREybD/MAhPE/KE"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 102}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": null, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "73braO219DIqMcrggzpuH9"}, {"__type__": "e0139aGBBFIBLYUi7ZvSe2q", "_name": "", "_objFlags": 0, "node": {"__id__": 102}, "_enabled": true, "_id": "a11RT9RH1N96fOCECmIw46"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 93}, "_enabled": true, "_normalMaterial": {"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}, "_grayMaterial": null, "duration": 0.1, "zoomScale": 1.2, "clickEvents": [{"__id__": 106}], "_N$interactable": true, "_N$enableAutoGrayEffect": false, "_N$transition": 1, "transition": 1, "_N$normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$pressedColor": {"__type__": "cc.Color", "r": 150, "g": 150, "b": 150, "a": 255}, "pressedColor": {"__type__": "cc.Color", "r": 150, "g": 150, "b": 150, "a": 255}, "_N$hoverColor": {"__type__": "cc.Color", "r": 200, "g": 200, "b": 200, "a": 255}, "hoverColor": {"__type__": "cc.Color", "r": 200, "g": 200, "b": 200, "a": 255}, "_N$disabledColor": {"__type__": "cc.Color", "r": 120, "g": 120, "b": 120, "a": 200}, "_N$normalSprite": null, "_N$pressedSprite": {"__uuid__": "e9ec654c-97a2-4787-9325-e6a10375219a"}, "pressedSprite": {"__uuid__": "e9ec654c-97a2-4787-9325-e6a10375219a"}, "_N$hoverSprite": {"__uuid__": "f0048c10-f03e-4c97-b9d3-3506e1d58952"}, "hoverSprite": {"__uuid__": "f0048c10-f03e-4c97-b9d3-3506e1d58952"}, "_N$disabledSprite": {"__uuid__": "29158224-f8dd-4661-a796-1ffab537140e"}, "_N$target": {"__id__": 94}, "_id": "8eV3tZXr1NJ7sn3w1OtYHB"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 30}, "component": "", "_componentId": "614e6IrDfpOdbOzK09O8Qzm", "handler": "clickSideButton", "customEventData": "4"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 93}, "_enabled": true, "alignMode": 2, "_target": null, "_alignFlags": 8, "_left": 0, "_right": 100, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 100, "_originalHeight": 0, "_id": "c3rdy3mOROoIOS6ft41No/"}, {"__type__": "cc.Node", "_name": "peachButton", "_objFlags": 0, "_parent": {"__id__": 32}, "_children": [{"__id__": 109}, {"__id__": 113}, {"__id__": 117}], "_active": true, "_components": [{"__id__": 120}, {"__id__": 122}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 300, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [120, -312.5, 0, 0, 0, 0, 1, 0.8, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "59R38Es2BNA7WlUlSt60db"}, {"__type__": "cc.Node", "_name": "peachButtonBg", "_objFlags": 512, "_parent": {"__id__": 108}, "_children": [], "_active": true, "_components": [{"__id__": 110}, {"__id__": 111}, {"__id__": 112}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 300, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "21Yxay5JxPTbHl8t2W3WHi"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 109}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "8cdb44ac-a3f6-449f-b354-7cd48cf84061"}, "_type": 1, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "94hrJ31RtFtoKaUjF3Q0YL"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 109}, "_enabled": true, "alignMode": 0, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 100, "_originalHeight": 40, "_id": "46/rewlrlAN5k7faBPp1CO"}, {"__type__": "e0139aGBBFIBLYUi7ZvSe2q", "_name": "", "_objFlags": 0, "node": {"__id__": 109}, "_enabled": true, "_id": "0fIK05PNNKDqNbvqkZFu4f"}, {"__type__": "cc.Node", "_name": "buttonShadow", "_objFlags": 0, "_parent": {"__id__": 108}, "_children": [], "_active": true, "_components": [{"__id__": 114}, {"__id__": 115}, {"__id__": 116}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 334, "height": 128}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -8, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "0fVtv18X5OuoAWQcwjfgCT"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 113}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": null, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": null, "_id": "d1KJ7tJdBIx7stLcm6X6fk"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 113}, "_enabled": true, "alignMode": 2, "_target": null, "_alignFlags": 45, "_left": -17, "_right": -17, "_top": -6, "_bottom": -22, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 278, "_originalHeight": 128, "_id": "0cBrRGx2dLFLYe+xnNBQtN"}, {"__type__": "e0139aGBBFIBLYUi7ZvSe2q", "_name": "", "_objFlags": 0, "node": {"__id__": 113}, "_enabled": true, "_id": "1dFBF6+sdMvoPG4Pv1ZtLo"}, {"__type__": "cc.Node", "_name": "lock", "_objFlags": 0, "_parent": {"__id__": 108}, "_children": [], "_active": true, "_components": [{"__id__": 118}, {"__id__": 119}], "_prefab": null, "_opacity": 0, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 35, "height": 40}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "7dZqsnXPhJLLARiKPki3mH"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 117}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": null, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "15G1pv3gNIHY+3OvlyMrLz"}, {"__type__": "e0139aGBBFIBLYUi7ZvSe2q", "_name": "", "_objFlags": 0, "node": {"__id__": 117}, "_enabled": true, "_id": "7ffSUO9tdKX6q7b5vUt9Km"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 108}, "_enabled": true, "_normalMaterial": {"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}, "_grayMaterial": null, "duration": 0.1, "zoomScale": 1.2, "clickEvents": [{"__id__": 121}], "_N$interactable": true, "_N$enableAutoGrayEffect": false, "_N$transition": 1, "transition": 1, "_N$normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$pressedColor": {"__type__": "cc.Color", "r": 150, "g": 150, "b": 150, "a": 255}, "pressedColor": {"__type__": "cc.Color", "r": 150, "g": 150, "b": 150, "a": 255}, "_N$hoverColor": {"__type__": "cc.Color", "r": 200, "g": 200, "b": 200, "a": 255}, "hoverColor": {"__type__": "cc.Color", "r": 200, "g": 200, "b": 200, "a": 255}, "_N$disabledColor": {"__type__": "cc.Color", "r": 120, "g": 120, "b": 120, "a": 200}, "_N$normalSprite": null, "_N$pressedSprite": {"__uuid__": "e9ec654c-97a2-4787-9325-e6a10375219a"}, "pressedSprite": {"__uuid__": "e9ec654c-97a2-4787-9325-e6a10375219a"}, "_N$hoverSprite": {"__uuid__": "f0048c10-f03e-4c97-b9d3-3506e1d58952"}, "hoverSprite": {"__uuid__": "f0048c10-f03e-4c97-b9d3-3506e1d58952"}, "_N$disabledSprite": {"__uuid__": "29158224-f8dd-4661-a796-1ffab537140e"}, "_N$target": {"__id__": 109}, "_id": "e27WWbW/dAoajkW/KTWosH"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 30}, "component": "", "_componentId": "614e6IrDfpOdbOzK09O8Qzm", "handler": "clickSideButton", "customEventData": "5"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 108}, "_enabled": true, "alignMode": 2, "_target": null, "_alignFlags": 8, "_left": 0, "_right": 100, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 100, "_originalHeight": 0, "_id": "aff9P2gVlOb71fwwPkWuHz"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 32}, "_enabled": true, "alignMode": 2, "_target": null, "_alignFlags": 10, "_left": -30, "_right": 0, "_top": 402.5, "_bottom": 402.5, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": "02FvEisBZI/ZMkCMxPg5s/"}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "node": {"__id__": 32}, "_enabled": true, "_layoutSize": {"__type__": "cc.Size", "width": 300, "height": 725}, "_resize": 1, "_N$layoutType": 2, "_N$cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_N$startAxis": 0, "_N$paddingLeft": 0, "_N$paddingRight": 0, "_N$paddingTop": 0, "_N$paddingBottom": 0, "_N$spacingX": 0, "_N$spacingY": 25, "_N$verticalDirection": 1, "_N$horizontalDirection": 0, "_N$affectedByScale": false, "_id": "bfWtTnwkNF8I62a+2wFYoQ"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 31}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 0, "_top": 160, "_bottom": 160, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": "c67jwHjKlPvKlQCIzTqsuo"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 30}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": null, "_type": 1, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "99w2RNoGxIYoghgM3YAiB0"}, {"__type__": "<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 30}, "_enabled": true, "horizontal": true, "vertical": false, "inertia": true, "brake": 1, "elastic": true, "bounceDuration": 0.23, "scrollEvents": [{"__id__": 128}], "cancelInnerEvents": false, "_N$content": {"__id__": 28}, "content": {"__id__": 28}, "_N$horizontalScrollBar": null, "_N$verticalScrollBar": null, "_id": "c0b07o2UBOdLViDXzjUa4i"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 30}, "component": "", "_componentId": "614e6IrDfpOdbOzK09O8Qzm", "handler": "scorllEvent", "customEventData": ""}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 30}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 240, "_originalHeight": 250, "_id": "39vtIUwERE8btqJILsYUxy"}, {"__type__": "614e6IrDfpOdbOzK09O8Qzm", "_name": "", "_objFlags": 0, "node": {"__id__": 30}, "_enabled": true, "scrollView": {"__id__": 127}, "view": {"__id__": 29}, "content": {"__id__": 28}, "layout": {"__id__": 131}, "sideButton": {"__id__": 32}, "unlockLabel": {"__id__": 8}, "_id": "e1VpWHU0RBGowPtAQTHsp4"}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "node": {"__id__": 28}, "_enabled": true, "_layoutSize": {"__type__": "cc.Size", "width": 3400, "height": 400}, "_resize": 1, "_N$layoutType": 1, "_N$cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_N$startAxis": 0, "_N$paddingLeft": 0, "_N$paddingRight": 0, "_N$paddingTop": 0, "_N$paddingBottom": 0, "_N$spacingX": 200, "_N$spacingY": 0, "_N$verticalDirection": 1, "_N$horizontalDirection": 0, "_N$affectedByScale": true, "_id": "f8XLjGHIRGIp6Sr3D/PXG2"}, {"__type__": "cc.Mask", "_name": "", "_objFlags": 0, "node": {"__id__": 29}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_spriteFrame": null, "_type": 0, "_segments": 64, "_N$alphaThreshold": 0, "_N$inverted": false, "_id": "f7/PCcNspMgLcDfosBonau"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 29}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 240, "_originalHeight": 250, "_id": "feCzwSDw5LLLyddvRWQcUj"}, {"__type__": "cc.Node", "_name": "blue", "_objFlags": 0, "_parent": {"__id__": 28}, "_children": [{"__id__": 135}, {"__id__": 153}], "_active": true, "_components": [{"__id__": 157}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 400, "height": 400}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [200, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "f33RFTrx5CGLmS2h6Rq64G"}, {"__type__": "cc.Node", "_name": "fourBox", "_objFlags": 0, "_parent": {"__id__": 134}, "_children": [{"__id__": 136}, {"__id__": 140}, {"__id__": 144}, {"__id__": 148}], "_active": true, "_components": [{"__id__": 152}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 400, "height": 400}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "c8Om6gJTNM9b29OD9PUIfW"}, {"__type__": "cc.Node", "_name": "blueTL", "_objFlags": 0, "_parent": {"__id__": 135}, "_children": [], "_active": true, "_components": [{"__id__": 137}, {"__id__": 138}, {"__id__": 139}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 200, "height": 200}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-100, 100, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "038JIRG6RFfIA6WFqdZ24G"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 136}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "8cdb44ac-a3f6-449f-b354-7cd48cf84061"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "b0u7ZmbyNIya3CLydzpSTE"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 136}, "_enabled": true, "alignMode": 2, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 0.5, "_top": 0, "_bottom": 0.5, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": false, "_isAbsTop": true, "_isAbsBottom": false, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 40, "_originalHeight": 36, "_id": "73TUSxKjlNrq9qkq2OIAMK"}, {"__type__": "e0139aGBBFIBLYUi7ZvSe2q", "_name": "", "_objFlags": 0, "node": {"__id__": 136}, "_enabled": true, "_id": "02cGNi+aFCbqzPGXqCoEaF"}, {"__type__": "cc.Node", "_name": "blueTR", "_objFlags": 0, "_parent": {"__id__": 135}, "_children": [], "_active": true, "_components": [{"__id__": 141}, {"__id__": 142}, {"__id__": 143}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 200, "height": 200}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [100, 100, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "bcX2TeT8RGx72DSrf11SPO"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 140}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "8cdb44ac-a3f6-449f-b354-7cd48cf84061"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "79MIi1GDxHkIe20zJhTlQd"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 140}, "_enabled": true, "alignMode": 2, "_target": null, "_alignFlags": 45, "_left": 0.5, "_right": 0, "_top": 0, "_bottom": 0.5, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": false, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": false, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 40, "_originalHeight": 36, "_id": "a8HHWrJwlLLbjY4O22vlUf"}, {"__type__": "e0139aGBBFIBLYUi7ZvSe2q", "_name": "", "_objFlags": 0, "node": {"__id__": 140}, "_enabled": true, "_id": "e7koHlX7FEx7ssC9+bVoIh"}, {"__type__": "cc.Node", "_name": "blueBL", "_objFlags": 0, "_parent": {"__id__": 135}, "_children": [], "_active": true, "_components": [{"__id__": 145}, {"__id__": 146}, {"__id__": 147}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 200, "height": 200}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-100, -100, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "e7POu/wQBCzazGlcwZ5QzH"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 144}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "8cdb44ac-a3f6-449f-b354-7cd48cf84061"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "9dLwXnTP9KPZLHYRZ3CS0Y"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 144}, "_enabled": true, "alignMode": 2, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 0.5, "_top": 0.5, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": false, "_isAbsTop": false, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 40, "_originalHeight": 36, "_id": "f6q5F9fR9AipHDp/sI3RFr"}, {"__type__": "e0139aGBBFIBLYUi7ZvSe2q", "_name": "", "_objFlags": 0, "node": {"__id__": 144}, "_enabled": true, "_id": "03WBfslZJIcLDIlzYg2UZS"}, {"__type__": "cc.Node", "_name": "blueBR", "_objFlags": 0, "_parent": {"__id__": 135}, "_children": [], "_active": true, "_components": [{"__id__": 149}, {"__id__": 150}, {"__id__": 151}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 200, "height": 200}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [100, -100, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "99TL2VIxxKaqidaK8mnj/M"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 148}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "8cdb44ac-a3f6-449f-b354-7cd48cf84061"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "a5grIhs1tKcqs4c/e4E4vW"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 148}, "_enabled": true, "alignMode": 2, "_target": null, "_alignFlags": 45, "_left": 0.5, "_right": 0, "_top": 0.5, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": false, "_isAbsRight": true, "_isAbsTop": false, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 40, "_originalHeight": 36, "_id": "a1k226rexHpKDq3G75IvSQ"}, {"__type__": "e0139aGBBFIBLYUi7ZvSe2q", "_name": "", "_objFlags": 0, "node": {"__id__": 148}, "_enabled": true, "_id": "83fOt4MyRDso0CY0p9pRn2"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 135}, "_enabled": true, "alignMode": 2, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": "7bXqNVa5FDELZABUnUKJJg"}, {"__type__": "cc.Node", "_name": "border", "_objFlags": 0, "_parent": {"__id__": 134}, "_children": [], "_active": true, "_components": [{"__id__": 154}, {"__id__": 155}, {"__id__": 156}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 436, "height": 436}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "529EriXWZBBZubWJvoPVzq"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 153}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": null, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": null, "_id": "634e8oWq9Cv5OttxSri5aK"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 153}, "_enabled": true, "alignMode": 2, "_target": null, "_alignFlags": 45, "_left": -18, "_right": -18, "_top": -18, "_bottom": -18, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 40, "_originalHeight": 36, "_id": "12NlUNlv1LXZxX4WKYL2pb"}, {"__type__": "e0139aGBBFIBLYUi7ZvSe2q", "_name": "", "_objFlags": 0, "node": {"__id__": 153}, "_enabled": true, "_id": "15E1cab/1B+YEkwFbWA6fg"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 134}, "_enabled": true, "alignMode": 2, "_target": null, "_alignFlags": 2, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": "edqIBbTptGEJGqIRIv/VKV"}, {"__type__": "cc.Node", "_name": "apple", "_objFlags": 0, "_parent": {"__id__": 28}, "_children": [{"__id__": 159}, {"__id__": 177}], "_active": true, "_components": [{"__id__": 181}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 400, "height": 400}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [800, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "6cKEtdgSRCP7Vb6r5+SFOX"}, {"__type__": "cc.Node", "_name": "fourBox", "_objFlags": 0, "_parent": {"__id__": 158}, "_children": [{"__id__": 160}, {"__id__": 164}, {"__id__": 168}, {"__id__": 172}], "_active": true, "_components": [{"__id__": 176}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 400, "height": 400}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "51uT6mPpRGWbA3i5Oi+/P3"}, {"__type__": "cc.Node", "_name": "appleTL", "_objFlags": 0, "_parent": {"__id__": 159}, "_children": [], "_active": true, "_components": [{"__id__": 161}, {"__id__": 162}, {"__id__": 163}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 200, "height": 200}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-100, 100, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "b8wZsDpyFNRqASA8A2wIm7"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 160}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "8cdb44ac-a3f6-449f-b354-7cd48cf84061"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "08S2K9w8ZCo4gIUQWAC6R3"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 160}, "_enabled": true, "alignMode": 2, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 0.5, "_top": 0, "_bottom": 0.5, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": false, "_isAbsTop": true, "_isAbsBottom": false, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 40, "_originalHeight": 36, "_id": "47qaJYbHVLlY7To3ksN2GH"}, {"__type__": "e0139aGBBFIBLYUi7ZvSe2q", "_name": "", "_objFlags": 0, "node": {"__id__": 160}, "_enabled": true, "_id": "19gbSp20pPsI/WnHSlb2mk"}, {"__type__": "cc.Node", "_name": "appleTR", "_objFlags": 0, "_parent": {"__id__": 159}, "_children": [], "_active": true, "_components": [{"__id__": 165}, {"__id__": 166}, {"__id__": 167}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 200, "height": 200}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [100, 100, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "b688uSQBpOEbxgj4+6m7Fz"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 164}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "8cdb44ac-a3f6-449f-b354-7cd48cf84061"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "40QuNkTcdLzrWcfkzVfK6K"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 164}, "_enabled": true, "alignMode": 2, "_target": null, "_alignFlags": 45, "_left": 0.5, "_right": 0, "_top": 0, "_bottom": 0.5, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": false, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": false, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 40, "_originalHeight": 36, "_id": "e5DACQs4xOcrYQ0Gu1Skiy"}, {"__type__": "e0139aGBBFIBLYUi7ZvSe2q", "_name": "", "_objFlags": 0, "node": {"__id__": 164}, "_enabled": true, "_id": "27M676fx1Mt4TohoFznVFm"}, {"__type__": "cc.Node", "_name": "appleBL", "_objFlags": 0, "_parent": {"__id__": 159}, "_children": [], "_active": true, "_components": [{"__id__": 169}, {"__id__": 170}, {"__id__": 171}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 200, "height": 200}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-100, -100, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "3fF+id7K9KXJYG4pNXYwBo"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 168}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "8cdb44ac-a3f6-449f-b354-7cd48cf84061"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "ffGtueIz5HKZYOo5bqKNYf"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 168}, "_enabled": true, "alignMode": 2, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 0.5, "_top": 0.5, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": false, "_isAbsTop": false, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 40, "_originalHeight": 36, "_id": "4eLjjAsRNJiZJTkOffdvhh"}, {"__type__": "e0139aGBBFIBLYUi7ZvSe2q", "_name": "", "_objFlags": 0, "node": {"__id__": 168}, "_enabled": true, "_id": "31VSy7EW1BqpIq6WkrZI/p"}, {"__type__": "cc.Node", "_name": "appleBR", "_objFlags": 0, "_parent": {"__id__": 159}, "_children": [], "_active": true, "_components": [{"__id__": 173}, {"__id__": 174}, {"__id__": 175}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 200, "height": 200}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [100, -100, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "568C/OqmJDcoVJFchiEQOA"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 172}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "8cdb44ac-a3f6-449f-b354-7cd48cf84061"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "96afdS/PNG5KFrLuhL2KP+"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 172}, "_enabled": true, "alignMode": 2, "_target": null, "_alignFlags": 45, "_left": 0.5, "_right": 0, "_top": 0.5, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": false, "_isAbsRight": true, "_isAbsTop": false, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 40, "_originalHeight": 36, "_id": "72vHbL9FhLobanV8zcPdfo"}, {"__type__": "e0139aGBBFIBLYUi7ZvSe2q", "_name": "", "_objFlags": 0, "node": {"__id__": 172}, "_enabled": true, "_id": "aaEbuX9sxEj49iEZ9UgP8s"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 159}, "_enabled": true, "alignMode": 2, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": "fcyIDGCwBMM5uMu1FJRcb9"}, {"__type__": "cc.Node", "_name": "border", "_objFlags": 0, "_parent": {"__id__": 158}, "_children": [], "_active": true, "_components": [{"__id__": 178}, {"__id__": 179}, {"__id__": 180}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 436, "height": 436}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "21E30pWRZMJJi25fCi8S92"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 177}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": null, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": null, "_id": "84BJRZg+hGQYF5YlaU0spD"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 177}, "_enabled": true, "alignMode": 2, "_target": null, "_alignFlags": 45, "_left": -18, "_right": -18, "_top": -18, "_bottom": -18, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 40, "_originalHeight": 36, "_id": "129cOW7gdPgIOHZ0B0oOv4"}, {"__type__": "e0139aGBBFIBLYUi7ZvSe2q", "_name": "", "_objFlags": 0, "node": {"__id__": 177}, "_enabled": true, "_id": "4ajvUbqP1PHb8XcN9ksvyP"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 158}, "_enabled": true, "alignMode": 2, "_target": null, "_alignFlags": 2, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": "f2FM4rMqtGvKcaIExZbOKj"}, {"__type__": "cc.Node", "_name": "orange", "_objFlags": 0, "_parent": {"__id__": 28}, "_children": [{"__id__": 183}, {"__id__": 201}], "_active": true, "_components": [{"__id__": 205}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 400, "height": 400}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [1400, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "ddOyk5yNxDk5eCVtETwDny"}, {"__type__": "cc.Node", "_name": "fourBox", "_objFlags": 0, "_parent": {"__id__": 182}, "_children": [{"__id__": 184}, {"__id__": 188}, {"__id__": 192}, {"__id__": 196}], "_active": true, "_components": [{"__id__": 200}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 400, "height": 400}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "d1OBjZm7VPKJwbZaUuBaL7"}, {"__type__": "cc.Node", "_name": "orangeTL", "_objFlags": 0, "_parent": {"__id__": 183}, "_children": [], "_active": true, "_components": [{"__id__": 185}, {"__id__": 186}, {"__id__": 187}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 200, "height": 200}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-100, 100, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "0dTvcvuANJCJ5lQryaZEr8"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 184}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "8cdb44ac-a3f6-449f-b354-7cd48cf84061"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "46MOhvhzZAn7rUzw81BxBA"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 184}, "_enabled": true, "alignMode": 2, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 0.5, "_top": 0, "_bottom": 0.5, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": false, "_isAbsTop": true, "_isAbsBottom": false, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 40, "_originalHeight": 36, "_id": "d4j+d9BoVGQJtGPh4N4CYD"}, {"__type__": "e0139aGBBFIBLYUi7ZvSe2q", "_name": "", "_objFlags": 0, "node": {"__id__": 184}, "_enabled": true, "_id": "26DmOUcxNHYofiuSCEiQVh"}, {"__type__": "cc.Node", "_name": "orangeTR", "_objFlags": 0, "_parent": {"__id__": 183}, "_children": [], "_active": true, "_components": [{"__id__": 189}, {"__id__": 190}, {"__id__": 191}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 200, "height": 200}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [100, 100, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "5dF1vUOc1Md4zt+62wF1/E"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 188}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "8cdb44ac-a3f6-449f-b354-7cd48cf84061"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "bc6lRAlO1BWZyrq19t8VKO"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 188}, "_enabled": true, "alignMode": 2, "_target": null, "_alignFlags": 45, "_left": 0.5, "_right": 0, "_top": 0, "_bottom": 0.5, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": false, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": false, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 40, "_originalHeight": 36, "_id": "1by8PFP7NKM5pDm63kbGTJ"}, {"__type__": "e0139aGBBFIBLYUi7ZvSe2q", "_name": "", "_objFlags": 0, "node": {"__id__": 188}, "_enabled": true, "_id": "5aRcY5+8FFNZG/H+y4A5aJ"}, {"__type__": "cc.Node", "_name": "orangeBL", "_objFlags": 0, "_parent": {"__id__": 183}, "_children": [], "_active": true, "_components": [{"__id__": 193}, {"__id__": 194}, {"__id__": 195}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 200, "height": 200}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-100, -100, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "8d4EFcx2dJ/LEFgOckKsJ5"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 192}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "8cdb44ac-a3f6-449f-b354-7cd48cf84061"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "69+HoUtEREyq89BX0bfjDA"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 192}, "_enabled": true, "alignMode": 2, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 0.5, "_top": 0.5, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": false, "_isAbsTop": false, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 40, "_originalHeight": 36, "_id": "a54B0TQwJCWrv3WHtGb65l"}, {"__type__": "e0139aGBBFIBLYUi7ZvSe2q", "_name": "", "_objFlags": 0, "node": {"__id__": 192}, "_enabled": true, "_id": "556hdjnthNL55vtGYHshm5"}, {"__type__": "cc.Node", "_name": "orangeBR", "_objFlags": 0, "_parent": {"__id__": 183}, "_children": [], "_active": true, "_components": [{"__id__": 197}, {"__id__": 198}, {"__id__": 199}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 200, "height": 200}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [100, -100, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "5eYXw8LopKtruc2acLzm5p"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 196}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "8cdb44ac-a3f6-449f-b354-7cd48cf84061"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "5bIFwadoNEGKq+GEgiSqxd"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 196}, "_enabled": true, "alignMode": 2, "_target": null, "_alignFlags": 45, "_left": 0.5, "_right": 0, "_top": 0.5, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": false, "_isAbsRight": true, "_isAbsTop": false, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 40, "_originalHeight": 36, "_id": "98zM5dWPJHiqE6hZImEi1W"}, {"__type__": "e0139aGBBFIBLYUi7ZvSe2q", "_name": "", "_objFlags": 0, "node": {"__id__": 196}, "_enabled": true, "_id": "38iN25YI1GH4WL6YER6TA/"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 183}, "_enabled": true, "alignMode": 2, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": "14X4hV+b5AxJ7Joaap7l60"}, {"__type__": "cc.Node", "_name": "border", "_objFlags": 0, "_parent": {"__id__": 182}, "_children": [], "_active": true, "_components": [{"__id__": 202}, {"__id__": 203}, {"__id__": 204}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 436, "height": 436}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "764CpTZztPuZFYiHlbof2r"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 201}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": null, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": null, "_id": "867zoVJAFCQJSNkOciYcvy"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 201}, "_enabled": true, "alignMode": 2, "_target": null, "_alignFlags": 45, "_left": -18, "_right": -18, "_top": -18, "_bottom": -18, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 40, "_originalHeight": 36, "_id": "f7HJg1zahDMK2tWhbtwoor"}, {"__type__": "e0139aGBBFIBLYUi7ZvSe2q", "_name": "", "_objFlags": 0, "node": {"__id__": 201}, "_enabled": true, "_id": "93y8X6kjtDY6yKqnsky0R5"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 182}, "_enabled": true, "alignMode": 2, "_target": null, "_alignFlags": 2, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": "f1WQzkGTZNE7aXIwoAR4TD"}, {"__type__": "cc.Node", "_name": "melon", "_objFlags": 0, "_parent": {"__id__": 28}, "_children": [{"__id__": 207}, {"__id__": 225}], "_active": true, "_components": [{"__id__": 229}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 400, "height": 400}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [2000, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "b8CnXeP1RLdrAz9OQa0yvU"}, {"__type__": "cc.Node", "_name": "fourBox", "_objFlags": 0, "_parent": {"__id__": 206}, "_children": [{"__id__": 208}, {"__id__": 212}, {"__id__": 216}, {"__id__": 220}], "_active": true, "_components": [{"__id__": 224}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 400, "height": 400}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "b94DocTehH75hojovWWTq4"}, {"__type__": "cc.Node", "_name": "melonTL", "_objFlags": 0, "_parent": {"__id__": 207}, "_children": [], "_active": true, "_components": [{"__id__": 209}, {"__id__": 210}, {"__id__": 211}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 200, "height": 200}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-100, 100, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "e4BuMDmm5Ecr2ALVi3GaB3"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 208}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "8cdb44ac-a3f6-449f-b354-7cd48cf84061"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "e3mBEZ1W9En6ea4v8GKSx9"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 208}, "_enabled": true, "alignMode": 2, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 0.5, "_top": 0, "_bottom": 0.5, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": false, "_isAbsTop": true, "_isAbsBottom": false, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 40, "_originalHeight": 36, "_id": "dae1slSVlJSqFPlErOt4ez"}, {"__type__": "e0139aGBBFIBLYUi7ZvSe2q", "_name": "", "_objFlags": 0, "node": {"__id__": 208}, "_enabled": true, "_id": "a6r3bzE6ZG9ID+JHbIKqkn"}, {"__type__": "cc.Node", "_name": "melonTR", "_objFlags": 0, "_parent": {"__id__": 207}, "_children": [], "_active": true, "_components": [{"__id__": 213}, {"__id__": 214}, {"__id__": 215}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 200, "height": 200}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [100, 100, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "d0xK6OplxP6bS8ur9bhXZY"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 212}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "8cdb44ac-a3f6-449f-b354-7cd48cf84061"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "d804ugHJdF/6C66tsvwx/n"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 212}, "_enabled": true, "alignMode": 2, "_target": null, "_alignFlags": 45, "_left": 0.5, "_right": 0, "_top": 0, "_bottom": 0.5, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": false, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": false, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 40, "_originalHeight": 36, "_id": "20chcK6YVFIYdQN4SGRskg"}, {"__type__": "e0139aGBBFIBLYUi7ZvSe2q", "_name": "", "_objFlags": 0, "node": {"__id__": 212}, "_enabled": true, "_id": "3cY9ir+9ZJX7dZ/Qln1HHR"}, {"__type__": "cc.Node", "_name": "melonBL", "_objFlags": 0, "_parent": {"__id__": 207}, "_children": [], "_active": true, "_components": [{"__id__": 217}, {"__id__": 218}, {"__id__": 219}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 200, "height": 200}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-100, -100, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "65obI8DY5Cy51NgZEl6e6a"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 216}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "8cdb44ac-a3f6-449f-b354-7cd48cf84061"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "2a32/quPdAZYXBasi78Psd"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 216}, "_enabled": true, "alignMode": 2, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 0.5, "_top": 0.5, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": false, "_isAbsTop": false, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 40, "_originalHeight": 36, "_id": "5cMlUAnCJMyYSNSLfbu7lY"}, {"__type__": "e0139aGBBFIBLYUi7ZvSe2q", "_name": "", "_objFlags": 0, "node": {"__id__": 216}, "_enabled": true, "_id": "87ih21C1JBsJVclr7/oUjM"}, {"__type__": "cc.Node", "_name": "melonBR", "_objFlags": 0, "_parent": {"__id__": 207}, "_children": [], "_active": true, "_components": [{"__id__": 221}, {"__id__": 222}, {"__id__": 223}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 200, "height": 200}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [100, -100, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "20ZudeqW1O9p/INH6jhjRo"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 220}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "8cdb44ac-a3f6-449f-b354-7cd48cf84061"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "39sjcDJ6xOzJO2+n/aduUV"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 220}, "_enabled": true, "alignMode": 2, "_target": null, "_alignFlags": 45, "_left": 0.5, "_right": 0, "_top": 0.5, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": false, "_isAbsRight": true, "_isAbsTop": false, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 40, "_originalHeight": 36, "_id": "a69WJHmexIiKiwiKI37mMR"}, {"__type__": "e0139aGBBFIBLYUi7ZvSe2q", "_name": "", "_objFlags": 0, "node": {"__id__": 220}, "_enabled": true, "_id": "dalAdrW0xK7ZWCElciXrvX"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 207}, "_enabled": true, "alignMode": 2, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": "c8TNEa5whFUbepBYDtxaMH"}, {"__type__": "cc.Node", "_name": "border", "_objFlags": 0, "_parent": {"__id__": 206}, "_children": [], "_active": true, "_components": [{"__id__": 226}, {"__id__": 227}, {"__id__": 228}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 436, "height": 436}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "b2QejXygVLxJF34jNs/xli"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 225}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": null, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": null, "_id": "53VHn/DrRAGb6xQPHJDnGp"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 225}, "_enabled": true, "alignMode": 2, "_target": null, "_alignFlags": 45, "_left": -18, "_right": -18, "_top": -18, "_bottom": -18, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 40, "_originalHeight": 36, "_id": "69vzZlfLNAz40vq7R3DSiO"}, {"__type__": "e0139aGBBFIBLYUi7ZvSe2q", "_name": "", "_objFlags": 0, "node": {"__id__": 225}, "_enabled": true, "_id": "07MDJrC1hNfaLlbIA3X7q1"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 206}, "_enabled": true, "alignMode": 2, "_target": null, "_alignFlags": 2, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": "04n5488jNHs7gSwmZb7Eej"}, {"__type__": "cc.Node", "_name": "berries", "_objFlags": 0, "_parent": {"__id__": 28}, "_children": [{"__id__": 231}, {"__id__": 249}], "_active": true, "_components": [{"__id__": 253}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 400, "height": 400}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [2600, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "0b9OgC7wlFQ77+G5dolLYr"}, {"__type__": "cc.Node", "_name": "fourBox", "_objFlags": 0, "_parent": {"__id__": 230}, "_children": [{"__id__": 232}, {"__id__": 236}, {"__id__": 240}, {"__id__": 244}], "_active": true, "_components": [{"__id__": 248}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 400, "height": 400}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "742pClyWJGg6ABl3IE17Fw"}, {"__type__": "cc.Node", "_name": "berriesTL", "_objFlags": 0, "_parent": {"__id__": 231}, "_children": [], "_active": true, "_components": [{"__id__": 233}, {"__id__": 234}, {"__id__": 235}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 200, "height": 200}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-100, 100, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "38Wp2pcTZD4L9SYXAwgmT8"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 232}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "8cdb44ac-a3f6-449f-b354-7cd48cf84061"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "3af52r6IFKDa7WMSPDtZIn"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 232}, "_enabled": true, "alignMode": 2, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 0.5, "_top": 0, "_bottom": 0.5, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": false, "_isAbsTop": true, "_isAbsBottom": false, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 40, "_originalHeight": 36, "_id": "38O4soDcZJkJwdiy1/b8EQ"}, {"__type__": "e0139aGBBFIBLYUi7ZvSe2q", "_name": "", "_objFlags": 0, "node": {"__id__": 232}, "_enabled": true, "_id": "cfycqKogRC2LTvIvo253C1"}, {"__type__": "cc.Node", "_name": "berriesTR", "_objFlags": 0, "_parent": {"__id__": 231}, "_children": [], "_active": true, "_components": [{"__id__": 237}, {"__id__": 238}, {"__id__": 239}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 200, "height": 200}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [100, 100, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "fbTTBkwIRITImLlXgi2xjQ"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 236}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "8cdb44ac-a3f6-449f-b354-7cd48cf84061"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "2d79ixZR5IXLjkmhbkNTdl"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 236}, "_enabled": true, "alignMode": 2, "_target": null, "_alignFlags": 45, "_left": 0.5, "_right": 0, "_top": 0, "_bottom": 0.5, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": false, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": false, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 40, "_originalHeight": 36, "_id": "00P0oU9gpBDbp4JEe0U4ml"}, {"__type__": "e0139aGBBFIBLYUi7ZvSe2q", "_name": "", "_objFlags": 0, "node": {"__id__": 236}, "_enabled": true, "_id": "219YJ/Sw9GbKjhdoEb3dZn"}, {"__type__": "cc.Node", "_name": "berriesBL", "_objFlags": 0, "_parent": {"__id__": 231}, "_children": [], "_active": true, "_components": [{"__id__": 241}, {"__id__": 242}, {"__id__": 243}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 200, "height": 200}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-100, -100, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "bbGFFHRxdL+4uAHRJ9HeI3"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 240}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "8cdb44ac-a3f6-449f-b354-7cd48cf84061"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "1d2EthCdtDc6M2S/ukgQuT"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 240}, "_enabled": true, "alignMode": 2, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 0.5, "_top": 0.5, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": false, "_isAbsTop": false, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 40, "_originalHeight": 36, "_id": "87meptkXpOpZl63kBpEIOX"}, {"__type__": "e0139aGBBFIBLYUi7ZvSe2q", "_name": "", "_objFlags": 0, "node": {"__id__": 240}, "_enabled": true, "_id": "12hnKtCUNF+ZqQhb+cyWmS"}, {"__type__": "cc.Node", "_name": "berriesBR", "_objFlags": 0, "_parent": {"__id__": 231}, "_children": [], "_active": true, "_components": [{"__id__": 245}, {"__id__": 246}, {"__id__": 247}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 200, "height": 200}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [100, -100, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "bdcWd2DPJLt6jb66wiCXdv"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 244}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "8cdb44ac-a3f6-449f-b354-7cd48cf84061"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "6erXWcJEVC1JNX03zs3iev"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 244}, "_enabled": true, "alignMode": 2, "_target": null, "_alignFlags": 45, "_left": 0.5, "_right": 0, "_top": 0.5, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": false, "_isAbsRight": true, "_isAbsTop": false, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 40, "_originalHeight": 36, "_id": "93Yof2aAdKyq5Zfw4Ow120"}, {"__type__": "e0139aGBBFIBLYUi7ZvSe2q", "_name": "", "_objFlags": 0, "node": {"__id__": 244}, "_enabled": true, "_id": "25kOTpbsFIBKae8uz9IRrE"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 231}, "_enabled": true, "alignMode": 2, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": "82C5FcbLZK2qY+7l9fjfup"}, {"__type__": "cc.Node", "_name": "border", "_objFlags": 0, "_parent": {"__id__": 230}, "_children": [], "_active": true, "_components": [{"__id__": 250}, {"__id__": 251}, {"__id__": 252}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 436, "height": 436}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "25E1R7ED1I55TqF2CS36St"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 249}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": null, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": null, "_id": "73jTE8qUVPWL6ldR1ixkcJ"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 249}, "_enabled": true, "alignMode": 2, "_target": null, "_alignFlags": 45, "_left": -18, "_right": -18, "_top": -18, "_bottom": -18, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 40, "_originalHeight": 36, "_id": "68vhzUoNJKOqfGoTSBxJfb"}, {"__type__": "e0139aGBBFIBLYUi7ZvSe2q", "_name": "", "_objFlags": 0, "node": {"__id__": 249}, "_enabled": true, "_id": "c81H2jGl5IT6tXU8OfHpgK"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 230}, "_enabled": true, "alignMode": 2, "_target": null, "_alignFlags": 2, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": "b4Srgt2+RGjJE7hwUHB9gw"}, {"__type__": "cc.Node", "_name": "peach", "_objFlags": 0, "_parent": {"__id__": 28}, "_children": [{"__id__": 255}, {"__id__": 273}], "_active": true, "_components": [{"__id__": 277}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 400, "height": 400}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [3200, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "8371GHta5IDbwkE+bJeMT9"}, {"__type__": "cc.Node", "_name": "fourBox", "_objFlags": 0, "_parent": {"__id__": 254}, "_children": [{"__id__": 256}, {"__id__": 260}, {"__id__": 264}, {"__id__": 268}], "_active": true, "_components": [{"__id__": 272}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 400, "height": 400}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "b0kZe2T2dIE7RXuyaXUo11"}, {"__type__": "cc.Node", "_name": "peachTL", "_objFlags": 0, "_parent": {"__id__": 255}, "_children": [], "_active": true, "_components": [{"__id__": 257}, {"__id__": 258}, {"__id__": 259}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 200, "height": 200}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-100, 100, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "fcaNLH5WNCg5T223qoyL7I"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 256}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "8cdb44ac-a3f6-449f-b354-7cd48cf84061"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "d6CtBmPCpFZI3Xvx0843ST"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 256}, "_enabled": true, "alignMode": 2, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 0.5, "_top": 0, "_bottom": 0.5, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": false, "_isAbsTop": true, "_isAbsBottom": false, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 40, "_originalHeight": 36, "_id": "8eFgHJP+REer7/m5DUjXvs"}, {"__type__": "e0139aGBBFIBLYUi7ZvSe2q", "_name": "", "_objFlags": 0, "node": {"__id__": 256}, "_enabled": true, "_id": "e50DdKGY5EurJvdqzJuO2a"}, {"__type__": "cc.Node", "_name": "peachTR", "_objFlags": 0, "_parent": {"__id__": 255}, "_children": [], "_active": true, "_components": [{"__id__": 261}, {"__id__": 262}, {"__id__": 263}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 200, "height": 200}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [100, 100, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "4bmkOlUuBLQbXlR7hL7egb"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 260}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "8cdb44ac-a3f6-449f-b354-7cd48cf84061"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "90W0FO2cRDfK4JR1NWND4B"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 260}, "_enabled": true, "alignMode": 2, "_target": null, "_alignFlags": 45, "_left": 0.5, "_right": 0, "_top": 0, "_bottom": 0.5, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": false, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": false, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 40, "_originalHeight": 36, "_id": "37QNuOAyxOGprL8ihmcZ0O"}, {"__type__": "e0139aGBBFIBLYUi7ZvSe2q", "_name": "", "_objFlags": 0, "node": {"__id__": 260}, "_enabled": true, "_id": "1dFgS+bxFAB59gjODyhOy4"}, {"__type__": "cc.Node", "_name": "peachBL", "_objFlags": 0, "_parent": {"__id__": 255}, "_children": [], "_active": true, "_components": [{"__id__": 265}, {"__id__": 266}, {"__id__": 267}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 200, "height": 200}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-100, -100, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "2ac6KgmsxGH6s6pOEFbzHv"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 264}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "8cdb44ac-a3f6-449f-b354-7cd48cf84061"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "acczDc405EaKDLPqjyx1Kw"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 264}, "_enabled": true, "alignMode": 2, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 0.5, "_top": 0.5, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": false, "_isAbsTop": false, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 40, "_originalHeight": 36, "_id": "b4BSMhqYpAZZy3HOO1YINz"}, {"__type__": "e0139aGBBFIBLYUi7ZvSe2q", "_name": "", "_objFlags": 0, "node": {"__id__": 264}, "_enabled": true, "_id": "12d5d9FetBRYYksQhCoanJ"}, {"__type__": "cc.Node", "_name": "peachBR", "_objFlags": 0, "_parent": {"__id__": 255}, "_children": [], "_active": true, "_components": [{"__id__": 269}, {"__id__": 270}, {"__id__": 271}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 200, "height": 200}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [100, -100, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "aetp3SKi5KhLxKmV/w4MGM"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 268}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "8cdb44ac-a3f6-449f-b354-7cd48cf84061"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "81G0L1NSRNHJJl0+CKI4SA"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 268}, "_enabled": true, "alignMode": 2, "_target": null, "_alignFlags": 45, "_left": 0.5, "_right": 0, "_top": 0.5, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": false, "_isAbsRight": true, "_isAbsTop": false, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 40, "_originalHeight": 36, "_id": "14GGPDxGhKl7A3tcFz/Krs"}, {"__type__": "e0139aGBBFIBLYUi7ZvSe2q", "_name": "", "_objFlags": 0, "node": {"__id__": 268}, "_enabled": true, "_id": "fdJsoSmthNfK3zm+KEFX7f"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 255}, "_enabled": true, "alignMode": 2, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": "a2R8MalAVGZpHiaaNkE+U3"}, {"__type__": "cc.Node", "_name": "border", "_objFlags": 0, "_parent": {"__id__": 254}, "_children": [], "_active": true, "_components": [{"__id__": 274}, {"__id__": 275}, {"__id__": 276}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 436, "height": 436}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "17lPJbbkxEL4GoYuc+N6r4"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 273}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": null, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": null, "_id": "584XrvHURCaryS0PHqtEKa"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 273}, "_enabled": true, "alignMode": 2, "_target": null, "_alignFlags": 45, "_left": -18, "_right": -18, "_top": -18, "_bottom": -18, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 40, "_originalHeight": 36, "_id": "34ylL7CzZJKoFjEAHvBds7"}, {"__type__": "e0139aGBBFIBLYUi7ZvSe2q", "_name": "", "_objFlags": 0, "node": {"__id__": 273}, "_enabled": true, "_id": "39D4BJ+HhEZo64zXitVSf0"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 254}, "_enabled": true, "alignMode": 2, "_target": null, "_alignFlags": 2, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": "b39dIBP2ZAKIPCqr4E5pls"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 28}, "_enabled": true, "alignMode": 2, "_target": null, "_alignFlags": 2, "_left": 0, "_right": 0, "_top": 0, "_bottom": 405, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 400, "_id": "1cwDxSCVlBg6SiHVc6MawB"}, {"__type__": "cc.Node", "_name": "background", "_objFlags": 0, "_parent": {"__id__": 2}, "_children": [{"__id__": 280}, {"__id__": 283}, {"__id__": 286}], "_active": true, "_components": [{"__id__": 289}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 2001, "height": 1125}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "84NsvlO75JL564CYmtJBza"}, {"__type__": "cc.Node", "_name": "white", "_objFlags": 0, "_parent": {"__id__": 279}, "_children": [], "_active": true, "_components": [{"__id__": 281}, {"__id__": 282}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 2001, "height": 1125}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "d0MGag9YRH0Yz9sYkpGii/"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 280}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "a23235d1-15db-4b95-8439-a2e005bfff91"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "fd6nTHZohK/olZnSNQu5uX"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 280}, "_enabled": true, "alignMode": 2, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 100, "_originalHeight": 100, "_id": "7fIZiHPG1Ac6K1dWtKI+p1"}, {"__type__": "cc.Node", "_name": "darkredTop", "_objFlags": 0, "_parent": {"__id__": 279}, "_children": [], "_active": true, "_components": [{"__id__": 284}, {"__id__": 285}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 208, "g": 144, "b": 134, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 2001, "height": 160}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 482.5, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "8b8TErpyxKcKrxt04heoYv"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 283}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "a23235d1-15db-4b95-8439-a2e005bfff91"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "7cQxdRM+xPpY19QG0GuXhX"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 283}, "_enabled": true, "alignMode": 2, "_target": null, "_alignFlags": 41, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 100, "_originalHeight": 0, "_id": "cdvWsiwUNCLLKMBx59mMdy"}, {"__type__": "cc.Node", "_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_objFlags": 0, "_parent": {"__id__": 279}, "_children": [], "_active": true, "_components": [{"__id__": 287}, {"__id__": 288}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 208, "g": 144, "b": 134, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 2001, "height": 160}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -482.5, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "58CsGlLtZKhKKpdOvo32cG"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 286}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "a23235d1-15db-4b95-8439-a2e005bfff91"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "8aQGsXYu9Irb+V+z9il7SC"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 286}, "_enabled": true, "alignMode": 2, "_target": null, "_alignFlags": 44, "_left": 0, "_right": 0, "_top": -50, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 100, "_originalHeight": 0, "_id": "a6u16xS7tA77tp5iLVWEpz"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 279}, "_enabled": true, "alignMode": 2, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": "413LNYYs5KQo8O7pqS//Lx"}, {"__type__": "cc.<PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 2}, "_enabled": true, "_designResolution": {"__type__": "cc.Size", "width": 2001, "height": 1125}, "_fitWidth": false, "_fitHeight": true, "_id": "59Cd0ovbdF4byw5sbjJDx7"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 2}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": "29zXboiXFBKoIV4PQ2liTe"}, {"__type__": "50cbeuVb8dIF6BjwsNUR42+", "_name": "", "_objFlags": 0, "node": {"__id__": 2}, "_enabled": true, "_id": "55sg2OB2pIC5ZNIUOswMkp"}, {"__type__": "038c5KGEvdP27b7o2XzlRIk", "_name": "", "_objFlags": 0, "node": {"__id__": 2}, "_enabled": true, "content": {"__id__": 28}, "_id": "f4JylPnkdE8ogxQusBZSgC"}]