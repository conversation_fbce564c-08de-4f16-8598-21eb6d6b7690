var cloudData = require('cloudUrl');
var userData = require('userData');
cc.Class({
    extends: cc.Component,

    properties: {
    },

    start() {
        // 常驻节点
        cc.game.addPersistRootNode(this.node);
        if(CC_WECHATGAME){
            // 创建背景音乐
            this.bgm1 = this.newBgm(cloudData.url.bgm1);
            this.bgm2 = this.newBgm(cloudData.url.bgm2);
            this.bgm3 = this.newBgm(cloudData.url.bgm3);
            this.curBgm = null

            // 创建音效
            this.effect_click = this.newEffect(cloudData.url.effect_click);
            this.effect_play = this.newEffect(cloudData.url.effect_play);
            this.effect_jump = this.newEffect(cloudData.url.effect_jump);
            this.effect_died = this.newEffect(cloudData.url.effect_died);

            // 创建语音
            this.voi_wbbb1 = this.newEffect(cloudData.url.voi_wbbb1);
            this.voi_wbbb2 = this.newEffect(cloudData.url.voi_wbbb2);
            this.voi_wbbb3 = this.newEffect(cloudData.url.voi_wbbb3);
            this.voi_wbbb4 = this.newEffect(cloudData.url.voi_wbbb4);
            this.voi_wbbb5 = this.newEffect(cloudData.url.voi_wbbb5);

            // 切换后台监听
            wx.onHide(()=>{
                if(this.curBgm!=null){
                    this.curBgm.pause();
                }
            });
            wx.onShow(()=>{
                if(this.curBgm!=null){
                    this.curBgm.play();
                }
            });
        } 
    },
    /**
     * 无缝播放背景音乐
     */
    newBgm(url){
            let audio = wx.createInnerAudioContext();
            let audioT = null;
            audio.src = url;
            audio.onPlay(()=>{
                this.curBgm.volume = userData.bgmVol;
                // 获取一次时长使onTimeUpdate生效
                audio.duration;//此处获取的时长为0
                // 播放进度改变时
                audio.onTimeUpdate(()=>{
                    clearTimeout(audioT);
                    // 提前重播100毫秒实现无缝播放
                    audioT = setTimeout(()=>{
                        audio.stop();
                        audio.play();
                    },Math.max((audio.duration-audio.currentTime)*1000-100, 1))
                    // 取消监听（只调用一次onTimeUpdate）
                    audio.offTimeUpdate();
                })
            });
            audio.onStop(()=>{
                clearTimeout(audioT);
            });
            audio.onPause(()=>{
                clearTimeout(audioT);
            });
            audio.onError(()=>{
                audio.destroy();
                clearTimeout(audioT);
            });

            return audio;
    },
    /**
     * 创建音效
     */
    newEffect(url){
            let audio = wx.createInnerAudioContext();
            audio.src = url;
            return audio;
    },
    /**
     * 播放背景音乐
     */
    playBgm(nextAudio){
        if(CC_WECHATGAME){
            // 如果当前正在播放背景音乐
            if(this.curBgm!=null){
                // 切换背景音乐
                this.changeBgm(nextAudio)
            }else{
                // 防止重复触发定时器
                // clearInterval(this.playBgmT);
                // clearInterval(this.stopBgmT);
                // 当前bgm
                this.curBgm = nextAudio;
                // 播放前音量设置为0
                // this.curBgm.volume = 0;
                this.curBgm.play();
                
                
                // // 音量淡入
                // this.playBgmT = setInterval(()=>{
                //     if(this.curBgm.volume >= userData.bgmVol){
                //         clearInterval(this.playBgmT);
                //     }
                //     // 逐步增加音量
                //     this.curBgm.volume = Math.min(userData.bgmVol,this.curBgm.volume+0.2);
                // },300)
            }
        }
    },
    /**
     * 切换背景音乐
     */
    changeBgm(nextAudio){
        if(CC_WECHATGAME){
            // 如果当前正在播放背景音乐
            if(this.curBgm!=null){
                // 防止重复触发定时器
                // clearInterval(this.playBgmT);
                // clearInterval(this.stopBgmT);
                // 暂停当前bgm
                this.curBgm.stop();
                this.curBgm = null;
                // 播放下一首
                if(nextAudio) this.playBgm(nextAudio);
                // // 音量淡出
                // this.stopBgmT = setInterval(()=>{
                //     if(this.curBgm.volume<=0){
                //         clearInterval(this.stopBgmT);
                //         // 暂停当前bgm
                //         this.curBgm.stop();
                //         this.curBgm = null;
                //         // 播放下一首
                //         if(nextAudio) this.playBgm(nextAudio);
                //     }
                //     // 逐步降低音量
                //     this.curBgm.volume = Math.max(0,this.curBgm.volume-0.2);
                // },300)
            }else{
                if(nextAudio) this.playBgm(nextAudio);
            }
        }
    },
    /**
     * 播放音效
     */
    playEffect(audio){
        if(CC_WECHATGAME){
            audio.volume = userData.effectVol;
            audio.stop();
            audio.play();
        }
    },
    /**
     * 播放语音
     */
    playVoi(audio){
        if(CC_WECHATGAME){
            if(this.currentVoi) this.currentVoi.stop();
            this.currentVoi = audio;
            this.currentVoi.volume = userData.effectVol;
            this.currentVoi.play();
        }
    },
    /**
     * 调整背景音乐音量
     */
    changeVol(){
        if(CC_WECHATGAME){
            if(this.curBgm){
                this.curBgm.volume = userData.bgmVol;
            }
        }
    }
});
