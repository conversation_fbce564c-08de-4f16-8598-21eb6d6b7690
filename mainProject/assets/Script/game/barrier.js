var cloudData = require('cloudUrl');
cc.Class({
    extends: cc.Component,

    properties: {
    },

    /**
     * 碰撞发生时
     * tag:
     *     land-0
     *     wall-1
     *     table-2、3
     *     prop-4
     *     yellowBarrier-5
     *     redBarrier-6
     *     jellyBarrier-7
     *     tableTemp-10、11
     *     scoreProp-40
     *     energyProp-41
     *     mapUpProp-42
     *     speedProp-43
     *     jelly-66
     */
    onBeginContact: function (contact, selfCollider, otherCollider){
        /*创建节点后的第一帧*/
        // 与其他组件重合则销毁节点
        if(!this.isCreate){
            // 不再接收碰撞回调
            this.node.getComponent(cc.RigidBody).enabledContactListener = false;
            // 销毁节点
            this.node.destroy();
            return;
        }

        /*创建节点成功后*/
        if(otherCollider.tag!=10&&otherCollider.tag!=11&&otherCollider.tag!=4&&otherCollider.tag!=40&&otherCollider.tag!=41&&otherCollider.tag!=42&&otherCollider.tag!=43){
            // 果冻碰到红色小鸡死亡
            if(otherCollider.tag ==66 && selfCollider.tag==6){
                // 等同于接触地面
                cc.find('Canvas/land').getComponent('land').crashLand();
            }
            // 果冻碰到果冻杯
            else if(otherCollider.tag ==66 && selfCollider.tag==7 && !this.isDes){
                if(CC_WECHATGAME){
                    // 播放音效
                    var audios = cc.find('audios').getComponent('audios');
                    audios.playEffect(audios.effect_play);
                }
                // 防止重复触发
                this.isDes = true;
                // 果冻杯记数加1
                cc.find('Canvas').getComponent('game').jellyBarrierNum +=1;
                // 不再接收碰撞回调
                this.node.getComponent(cc.RigidBody).enabledContactListener = false;
                // 销毁前动作
                this.node.runAction(cc.sequence(
                    cc.spawn(cc.scaleTo(0.2,2.7,2.7),cc.fadeOut(0.2)), cc.callFunc(()=>{
                        // 销毁节点
                        if(cc.isValid(this.node)) this.node.destroy();
                    })
                ));
            }else if(!this.isDes){
                // 防止重复触发
                this.isDes = true;
                // 不再接收碰撞回调
                this.node.getComponent(cc.RigidBody).enabledContactListener = false;
                // 销毁前动作
                this.node.runAction(cc.sequence(
                    cc.spawn(cc.scaleTo(0.2,0.2,0.2),cc.fadeOut(0.2)), cc.callFunc(()=>{
                        // 销毁节点
                        if(cc.isValid(this.node)) this.node.destroy();
                    })
                ));
            }
        }
    },
    lateUpdate(){
        // 创建节点第一帧过后标记为true
        this.isCreate = true;
    }
});
