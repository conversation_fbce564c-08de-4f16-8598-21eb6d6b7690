cc.Class({
    extends: cc.Component,

    properties: {
    	jelly: cc.Node,
        map: cc.Node,
        walls:cc.Node,
        land: cc.Node,
        ui: cc.Node
    },
    start(){
        this.node.width = cc.winSize.width;
        this.node.height = cc.winSize.height;
        //当屏幕大小发生改变时调用
        cc.view.setResizeCallback(()=>{
            this.map.getComponent('map').initMap();
            this.land.getComponent('land').initLand();
            this.walls.getComponent('walls').initWalls();
            this.ui.getComponent('ui').initUi();
            this.node.width = cc.winSize.width;
            this.node.height = cc.winSize.height;
        });
    },
    update (dt){
    	// var wpos = this.jelly.convertToWorldSpaceAR(cc.v2(0, 0));
        // var pos = this.node.parent.convertToNodeSpaceAR(wpos);
        // this.node.setPosition(pos); 
        this.node.x = this.jelly.x;
        this.node.y = this.jelly.y;
    }
});
