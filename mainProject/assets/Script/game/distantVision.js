cc.Class({
    extends: cc.Component,

    properties: {
        visionContent: cc.Node,
        jelly: cc.Node,
        land: cc.Node
    },

    onload(){
        // 贴图密度
        this.intensity = 0.5;
        this.visionContent.width = cc.winSize.width/this.intensity;
        this.visionContent.height = cc.winSize.height/this.intensity;
        this.node.width = cc.winSize.width*2/this.intensity;
    },

    start(){
        // 景深距离(0-1)
        this.distance = 0.4;
    },

    update(dt){
        // 地图跟随果冻移动（移动单位为贴图长宽）
        this.node.x = this.jelly.x*this.distance;
        this.node.y = this.jelly.y*this.distance+this.land.y;
        
        // 可视区高度
        let visonH = this.jelly.y*(1-this.distance)-this.land.y+cc.winSize.height;
        // 需要创建的content数量
        let visonNum = Math.ceil(visonH/this.visionContent.height)*2;
        // 当前content数量
        let currentNum = this.node.children.length;
        // 创建content
        for(let i=0; i<visonNum-currentNum; i++){
            let visionContent = cc.instantiate(this.visionContent);
            this.node.addChild(visionContent);
        }
    },
});
