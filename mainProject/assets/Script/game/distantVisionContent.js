var cloudData = require('cloudUrl');
cc.Class({
    extends: cc.Component,

    properties: {
    },

    start(){
        // 远景贴图
        if(CC_WECHATGAME){
            this.spriteList = [];
            this.spriteList.push(cloudData.ccData['distantVision1']);
            this.spriteList.push(cloudData.ccData['distantVision2']);
            this.spriteList.push(cloudData.ccData['distantVision3']);
            this.spriteList.push(cloudData.ccData['distantVision4']);
            this.spriteList.push(cloudData.ccData['distantVision1']);
            this.spriteList.push(cloudData.ccData['distantVision2']);
            this.spriteList.push(cloudData.ccData['distantVision7']);
        }
        for(let i in this.node.children){
            let SP = this.node.children[i].getChildByName('Sprite');
            // 随机坐标
            SP.x = Math.random()*SP.parent.width-SP.parent.width/2;
            SP.y = Math.random()*SP.parent.height-SP.parent.height/2;
            // 随机缩放
            SP.scale = Math.random()*0.5+0.5;// 0.5-1
            // 随机远景贴图
            if(CC_WECHATGAME){
                var randomIdx = Math.floor(Math.random()*this.spriteList.length);
                // 创建随机贴图
                SP.getComponent(cc.Sprite).spriteFrame = new cc.SpriteFrame(this.spriteList[randomIdx]);
            }
        }    
    }
});
