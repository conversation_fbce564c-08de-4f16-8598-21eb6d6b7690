var cloudData = require('cloudUrl');
var userData = require('userData');
cc.Class({
    extends: cc.Component,

    properties: {
        blueJelly: cc.Prefab,
        appleJelly: cc.Prefab,
        orangeJelly: cc.Prefab,
        melonJelly: cc.Prefab,
        berriesJelly: cc.Prefab,
        peachJelly: cc.Prefab
    },

    start(){
        // 切换果冻型号
        if(CC_WECHATGAME){
            let jelly;
            switch(userData.skin.currentSkin){
                case 'blue':
                    jelly = cc.instantiate(this.blueJelly);
                    this.getComponent(cc.Sprite).spriteFrame = new cc.SpriteFrame(cloudData.ccData['blueJelly']);
                    this.getBrokenSprite('blue');
                    break;
                case 'apple':
                    jelly = cc.instantiate(this.appleJelly);
                    this.getComponent(cc.Sprite).spriteFrame = new cc.SpriteFrame(cloudData.ccData['appleJelly']);
                    this.getBrokenSprite('apple');
                    break;
                case 'orange':
                    jelly = cc.instantiate(this.orangeJelly);
                    this.getComponent(cc.Sprite).spriteFrame = new cc.SpriteFrame(cloudData.ccData['orangeJelly']);
                    this.getBrokenSprite('orange');
                    break;
                case 'melon':
                    jelly = cc.instantiate(this.melonJelly);
                    this.getComponent(cc.Sprite).spriteFrame = new cc.SpriteFrame(cloudData.ccData['melonJelly']);
                    this.getBrokenSprite('melon');
                    break;
                case 'berries':
                    jelly = cc.instantiate(this.berriesJelly);
                    this.getComponent(cc.Sprite).spriteFrame = new cc.SpriteFrame(cloudData.ccData['berriesJelly']);
                    this.getBrokenSprite('berries');
                    break;
                case 'peach':
                    jelly = cc.instantiate(this.peachJelly);
                    this.getComponent(cc.Sprite).spriteFrame = new cc.SpriteFrame(cloudData.ccData['peachJelly']);
                    this.getBrokenSprite('peach');
                    break;
                default:
                    return;
            }
            this.node.anchorX = jelly.anchorX;
            this.node.anchorY = jelly.anchorY;
            this.node.width = jelly.width;
            this.node.height = jelly.height;
            this.node.getChildByName('broken').anchorX = jelly.getChildByName('broken').anchorX;
            this.node.getChildByName('broken').anchorY = jelly.getChildByName('broken').anchorY;
            this.node.getChildByName('broken').width = jelly.getChildByName('broken').width;
            this.node.getChildByName('broken').height = jelly.getChildByName('broken').height;
            this.getComponent(cc.PhysicsPolygonCollider).points = jelly.getComponent(cc.PhysicsPolygonCollider).points;
            // 更新物理组件
            this.getComponent(cc.PhysicsPolygonCollider).apply();
        }
    },

    getBrokenSprite(name){
        this.broken0 = new cc.SpriteFrame(cloudData.ccData[name+'Broken0']);
        this.broken1 = new cc.SpriteFrame(cloudData.ccData[name+'Broken1']);
        this.broken2 = new cc.SpriteFrame(cloudData.ccData[name+'Broken2']);
        this.broken3 = new cc.SpriteFrame(cloudData.ccData[name+'Broken3']);
    },

    /**
     * 碰撞发生时
     * tag:
     *     land-0
     *     wall-1
     *     table-2、3
     *     prop-4
     *     yellowBarrier-5
     *     redBarrier-6
     *     jellyBarrier-7
     *     tableTemp-10、11
     *     scoreProp-40
     *     energyProp-41
     *     mapUpProp-42
     *     speedProp-43
     *     jelly-66
     */
    onEndContact: function (contact, selfCollider, otherCollider){
        // 防止快速触发
        if(this.t) clearTimeout(this.t);
        this.t = setTimeout(()=>{
            if(otherCollider.tag==1 || otherCollider.tag==2 || otherCollider.tag==3 || otherCollider.tag==5){
                if(CC_WECHATGAME){
                    // 播放音效
                    var audios = cc.find('audios').getComponent('audios');
                    audios.playEffect(audios.effect_jump);
                }
                // 碰撞动作
                this.node.runAction(cc.sequence(
                    cc.scaleTo(0.1,0.8,0.8),cc.scaleTo(0.1,1.2,1.2),cc.scaleTo(0.1,0.9,0.9),cc.scaleTo(0.1,1.1,1.1),cc.scaleTo(0.1,1,1)
                ));
            }
            // 碰撞到平台正面时
            if(otherCollider.tag==2){
                let rigidBody = this.node.getComponent(cc.RigidBody);
                let v = Math.sqrt(Math.pow(rigidBody.linearVelocity.x,2)+Math.pow(rigidBody.linearVelocity.y,2));
                this.addV = cc.find('Canvas').getComponent('game').addV;
                // 增加线性速度
                rigidBody.linearVelocity = cc.v2(rigidBody.linearVelocity.x*(this.addV+v)/v,rigidBody.linearVelocity.y*(this.addV+v)/v);
            }
        },10);
    }
    
});
