var cloudData = require('cloudUrl');
cc.Class({
    extends: cc.Component,

    properties: {
        jelly: cc.Node,
        landContent: cc.Node,
        layout: cc.Node,
        settlementWin: cc.Node,
        moreLabel:cc.Node
    },
    start(){
        //初始化陆地长宽
        this.initLand();
        this.isCrash = false;
    },
    update (dt) {
        // 陆地跟随果冻水平移动（移动单位为贴图长宽）
        this.node.x = Math.floor(this.jelly.x/this.spriteW)*this.spriteW;
    },
    initLand(){
        // 陆地贴图宽度
        this.spriteW = this.landContent.width;
        // 设置陆地节点高度、宽度
        this.node.height = cc.winSize.height/2;
        this.node.width = cc.winSize.width+this.spriteW*2;
        // 创建陆地铺满可视区
        // 创建个数
        var num = Math.ceil(this.node.width/this.spriteW) - this.layout.children.length;
        for(let i=0; i<num; i++){
            let landContent = cc.instantiate(this.landContent);
            this.layout.addChild(landContent);
        }
    },
    
    /**
     * 果冻破碎
     */
    jellyBoom(){
        if(CC_WECHATGAME){
            // 播放音效
            var audios = cc.find('audios').getComponent('audios');
            audios.playEffect(audios.effect_died);
        }
        cc.find('Canvas').getComponent('game').endGame();
        // 上传分数、高度
        cc.find('Canvas').getComponent('game').uploadData();
        // 获取碎片显示到结算栏
        cc.find('Canvas').getComponent('game').getChip();
        let jellyJs = this.jelly.getComponent('jelly');
        // 果冻破碎
        this.jelly.runAction(
            cc.spawn(
                cc.fadeOut(0.4),cc.scaleTo(0.3,2,2),
                cc.sequence(
                    cc.callFunc(()=>{
                        this.jelly.getComponent(cc.Sprite).spriteFrame = null;
                        this.jelly.getChildByName('broken').getComponent(cc.Sprite).spriteFrame = jellyJs.broken0;
                    }),cc.delayTime(0.08),
                    cc.callFunc(()=>{
                        this.jelly.getChildByName('broken').getComponent(cc.Sprite).spriteFrame = jellyJs.broken1;
                    }),cc.delayTime(0.08),
                    cc.callFunc(()=>{
                        this.jelly.getChildByName('broken').getComponent(cc.Sprite).spriteFrame = jellyJs.broken2;
                    }),cc.delayTime(0.08),
                    cc.callFunc(()=>{
                        this.jelly.getChildByName('broken').getComponent(cc.Sprite).spriteFrame = jellyJs.broken3;
                    }),cc.delayTime(0.08),
                    cc.callFunc(()=>{
                        this.jelly.getChildByName('broken').getComponent(cc.Sprite).spriteFrame = null;
                    }),cc.delayTime(0.5),
                    cc.callFunc(()=>{
                        this.settlementWin.active = true;
                        this.settlementWin.getChildByName('textWinBg1').runAction(
                            cc.spawn(
                                cc.scaleTo(0.2,1,1),cc.fadeIn(0.3)
                            )
                        );
                        this.settlementWin.getChildByName('grayBg').runAction(cc.fadeTo(0.2,80));
                    })
                )
            )
        )
    },

    /**
     * 果冻落地
     */
    crashLand(){
        // 防止重复触发
        if(!this.isCrash){
            this.isCrash = true;
            if(cc.find('Canvas').getComponent('game').resurgence){
                this.jellyBoom();
            }else{
                this.moreLabel.getComponent('moreButtonFun').indexButton('gg');
            }
        }
    }
});
