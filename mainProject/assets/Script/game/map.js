var cloudData = require('cloudUrl');
cc.Class({
    extends: cc.Component,

    properties: {
        jelly: cc.Node,
        mapBg: cc.Node,
        mapLayout: cc.Node
    },
    start(){
        // 景深距离(0-1)
        this.distance = 0.6;
        // 初始化背景图长宽
        this.initMap();
    },
    update (dt) {
    	// 地图跟随果冻移动（移动单位为贴图长宽）
        this.node.x = this.jelly.x%(this.spriteW/this.distance)*this.distance+Math.floor(this.jelly.x/this.spriteW)*this.spriteW;
    	this.node.y = this.jelly.y%(this.spriteH/this.distance)*this.distance+Math.floor(this.jelly.y/this.spriteH)*this.spriteH;
    },
    initMap(){
    	// 背景贴图长宽
    	this.spriteW = this.mapBg.width*this.mapBg.scaleX;
    	this.spriteH = this.mapBg.height*this.mapBg.scaleY;
    	// 设置地图长宽为可视区
    	this.node.width = cc.winSize.width+this.spriteW*3+this.spriteW*this.distance*2;
    	this.node.height = cc.winSize.height+this.spriteH*2+this.spriteH*this.distance*2;

        var w = Math.ceil(this.node.width/this.spriteW);
        var h = Math.ceil(this.node.height/this.spriteH);
        // 将背景平铺至可视区长宽
        this.mapLayout.width = w*this.spriteW+w;
        this.mapLayout.height = h*this.spriteH+h;
        for(let i=0; i<w*h; i++){
            let map = cc.instantiate(this.mapBg);
            this.mapLayout.addChild(map);
        }
    }
});
