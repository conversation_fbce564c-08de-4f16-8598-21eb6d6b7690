var cloudData = require('cloudUrl');
cc.Class({
    extends: cc.Component,

    properties: {
    },

    start(){
        // 近景贴图
        if(CC_WECHATGAME){
            this.spriteList = [];
            this.spriteList.push(cloudData.ccData['nearVision1']);
            this.spriteList.push(cloudData.ccData['nearVision2']);
            this.spriteList.push(cloudData.ccData['nearVision3']);
            this.spriteList.push(cloudData.ccData['nearVision4']);
        }
        for(let i in this.node.children){
            let SP = this.node.children[i].getChildByName('Sprite');
            // 随机坐标
            SP.x = Math.random()*SP.parent.width-SP.parent.width/2;
            SP.y = Math.random()*SP.parent.height-SP.parent.height/2;
            // 随机缩放
            SP.scale = Math.random()*0.5+0.5;// 0.5-1
            // 随机近景贴图
            if(CC_WECHATGAME){
                var randomIdx = Math.floor(Math.random()*this.spriteList.length);
                // 创建随机贴图
                SP.getComponent(cc.Sprite).spriteFrame = new cc.SpriteFrame(this.spriteList[randomIdx]);
                // 同一个visionContent不创建两个 nearVision1
                if(randomIdx==0){
                    if(this.haveOne){
                        // 清除默认贴图
                        SP.getComponent(cc.Sprite).spriteFrame = null;
                    }
                    this.haveOne = true;
                }
            }
        }    
    }
});
