var cloudData = require('cloudUrl');
cc.Class({
    extends: cc.Component,

    properties: {
    },

    onLoad(){
        // 道具存在时间（1分钟）
        this.existT = setTimeout(()=>{
            if(cc.isValid(this.node)){
                // 销毁前动作
                this.node.runAction(cc.sequence(
                    cc.spawn(cc.scaleTo(0.2,0.2,0.2),cc.fadeOut(0.2)), cc.callFunc(()=>{
                        // 销毁节点
                        if(cc.isValid(this.node)) this.node.destroy();
                    })
                ));
            }
        },60000)
    },
    /**
     * 碰撞发生时
     * tag:
     *     land-0
     *     wall-1
     *     table-2、3
     *     prop-4
     *     yellowBarrier-5
     *     redBarrier-6
     *     jellyBarrier-7
     *     tableTemp-10、11
     *     scoreProp-40
     *     energyProp-41
     *     mapUpProp-42
     *     speedProp-43
     *     jelly-66
     */
    onBeginContact: function (contact, selfCollider, otherCollider){
        /*创建节点后的第一帧*/
        // 与其他组件重合则销毁节点
        if(!this.isCreate){
            // 不与未创建成功的table碰撞
            if(otherCollider.tag!=10&&otherCollider.tag!=11){
                // 不再接收碰撞回调
                this.node.getComponent(cc.RigidBody).enabledContactListener = false;
                // 销毁节点
                this.node.destroy();
                return;
            }
        }

        /*创建节点成功后*/
        // 不与未创建成功的table及prop碰撞
        if(otherCollider.tag!=10&&otherCollider.tag!=11&&otherCollider.tag!=4&&otherCollider.tag!=40&&otherCollider.tag!=41&&otherCollider.tag!=42&&otherCollider.tag!=43){
            // 果冻吃到道具
            if(otherCollider.tag==66 &&! this.isDes){
                if(CC_WECHATGAME){
                    // 播放音效
                    var audios = cc.find('audios').getComponent('audios');
                    audios.playEffect(audios.effect_play);
                }
                // 防止重复触发
                this.isDes = true;
                let gameJs = cc.find('Canvas').getComponent('game');
                // 道具标记
                switch(selfCollider.tag){
                    case 40:
                        // 积分道具
                        // 积分+50
                        gameJs.recordScore += 50
                        break;
                    case 41:
                        // 能量道具
                        // 能量+200
                        gameJs.updateEng(200);
                        break;
                    case 42:
                        // 地图下降速度降低道具
                        // 地图下降速度-200
                        gameJs.currentDown = Math.max(gameJs.minDown,gameJs.currentDown-200);
                        break;
                    case 43:
                        // 果冻速度道具
                        // 果冻最大线性速度+500，碰撞table正面时获得的线性速度+200，持续10秒
                        gameJs.jellySpeedProp(gameJs.maxSpeedCD);
                        break;
                }
                // 不再接收碰撞回调
                this.node.getComponent(cc.RigidBody).enabledContactListener = false;
                // 销毁前动作
                this.node.runAction(cc.sequence(
                    cc.spawn(cc.scaleTo(0.3,2.7,2.7),cc.fadeOut(0.3)), cc.callFunc(()=>{
                        // 销毁节点
                        if(cc.isValid(this.node)) this.node.destroy();
                    })
                ));
            }
            // 道具碰到障碍物
            else if(!this.isDes && otherCollider.tag==5 || otherCollider.tag==6 || otherCollider.tag==7){
                // 防止重复触发
                this.isDes = true;
                // 不再接收碰撞回调
                this.node.getComponent(cc.RigidBody).enabledContactListener = false;
                // 销毁前动作
                this.node.runAction(cc.sequence(
                    cc.spawn(cc.scaleTo(0.2,0.2,0.2),cc.fadeOut(0.2)), cc.callFunc(()=>{
                        // 销毁节点
                        if(cc.isValid(this.node)) this.node.destroy();
                    })
                ));
            }else if(!this.isDes){
                // 防止重复触发
                this.isDes = true;
                // 不再接收碰撞回调
                this.node.getComponent(cc.RigidBody).enabledContactListener = false;
                // 销毁节点
                this.node.destroy();
            }
        }
    },
    lateUpdate(){
        // 创建节点第一帧过后标记为true
        this.isCreate = true;
    }
});
