var cloudData = require('cloudUrl');
var userData = require('userData');
cc.Class({
    extends: cc.Component,

    properties: {
        textButton: cc.Node
    },

    start(){
        if(CC_WECHATGAME){
            // 动态加载按钮贴图
            this.textButton.getComponent(cc.Button).normalSprite = new cc.SpriteFrame(cloudData.ccData.textButtonBg);
            this.textButton.getComponent(cc.Button).normalSprite.insetBottom = 34;
            this.textButton.getComponent(cc.Button).normalSprite.insetLeft = 41;
            this.textButton.getComponent(cc.Button).normalSprite.insetRight = 41;
            this.textButton.getComponent(cc.Button).normalSprite.insetTop = 34;

            this.textButton.getComponent(cc.Button).pressedSprite = new cc.SpriteFrame(cloudData.ccData.textButtonBgDark);
            this.textButton.getComponent(cc.Button).pressedSprite.insetBottom = 34;
            this.textButton.getComponent(cc.Button).pressedSprite.insetLeft = 41;
            this.textButton.getComponent(cc.Button).pressedSprite.insetRight = 41;
            this.textButton.getComponent(cc.Button).pressedSprite.insetTop = 34;
        }
    },

    yesButton(){
        if(CC_WECHATGAME){
            // 播放音效
            var audios = cc.find('audios').getComponent('audios');
            audios.playEffect(audios.effect_click);
        }
        this.node.getChildByName('textWinBg1').runAction(
            cc.spawn(
                cc.scaleTo(0.2,0.5,0.5),cc.fadeOut(0.2)
            )
        );
        this.node.getChildByName('grayBg').runAction(
            cc.sequence(
                cc.fadeOut(0.2),cc.callFunc(()=>{
                    this.node.active = false
                    // 淡出
                    cc.find('Canvas').runAction(
                        cc.sequence(
                            cc.fadeOut(0.5),cc.callFunc(()=>{
                                cc.director.loadScene("index");
                            })
                        )
                    )
                })
            )
        );
        
    }
});
