var cloudData = require('cloudUrl');
var userData = require('userData');
cc.Class({
    extends: cc.Component,

    properties: {
    },
    
    start(){
        if(CC_WECHATGAME){
            switch(userData.skin.currentSkin){
                case 'blue':
                    this.getComponent(cc.Sprite).spriteFrame = new cc.SpriteFrame(cloudData.ccData['blueTable']);
                    this.getComponent(cc.Sprite).spriteFrame.insetBottom = 16;
                    this.getComponent(cc.Sprite).spriteFrame.insetLeft = 16;
                    this.getComponent(cc.Sprite).spriteFrame.insetRight = 16;
                    this.getComponent(cc.Sprite).spriteFrame.insetTop = 16;
                    break;
                case 'apple':
                    this.getComponent(cc.Sprite).spriteFrame = new cc.SpriteFrame(cloudData.ccData['appleTable']);
                    this.getComponent(cc.Sprite).spriteFrame.insetBottom = 16;
                    this.getComponent(cc.Sprite).spriteFrame.insetLeft = 16;
                    this.getComponent(cc.Sprite).spriteFrame.insetRight = 16;
                    this.getComponent(cc.Sprite).spriteFrame.insetTop = 16;
                    break;
                case 'orange':
                    this.getComponent(cc.Sprite).spriteFrame = new cc.SpriteFrame(cloudData.ccData['orangeTable']);
                    this.getComponent(cc.Sprite).spriteFrame.insetBottom = 3;
                    this.getComponent(cc.Sprite).spriteFrame.insetLeft = 2;
                    this.getComponent(cc.Sprite).spriteFrame.insetRight = 2;
                    this.getComponent(cc.Sprite).spriteFrame.insetTop = 3;
                    break;
                case 'melon':
                    this.getComponent(cc.Sprite).spriteFrame = new cc.SpriteFrame(cloudData.ccData['melonTable']);
                    this.getComponent(cc.Sprite).spriteFrame.insetBottom = 21;
                    this.getComponent(cc.Sprite).spriteFrame.insetLeft = 17;
                    this.getComponent(cc.Sprite).spriteFrame.insetRight = 23;
                    this.getComponent(cc.Sprite).spriteFrame.insetTop = 15;
                    this.node.anchorY = 0.57;
                    this.node.height = 46;
                    break;
                case 'berries':
                    this.getComponent(cc.Sprite).spriteFrame = new cc.SpriteFrame(cloudData.ccData['berriesTable']);
                    this.getComponent(cc.Sprite).spriteFrame.insetBottom = 17;
                    this.getComponent(cc.Sprite).spriteFrame.insetLeft = 22;
                    this.getComponent(cc.Sprite).spriteFrame.insetRight = 22;
                    this.getComponent(cc.Sprite).spriteFrame.insetTop = 17;
                    break;
                case 'peach':
                    this.getComponent(cc.Sprite).spriteFrame = new cc.SpriteFrame(cloudData.ccData['peachTable']);
                    this.getComponent(cc.Sprite).spriteFrame.insetBottom = 16;
                    this.getComponent(cc.Sprite).spriteFrame.insetLeft = 16;
                    this.getComponent(cc.Sprite).spriteFrame.insetRight = 16;
                    this.getComponent(cc.Sprite).spriteFrame.insetTop = 16;
                    break;
                default:
                    return;
            }
        }
        this.isContact10 = false;
        this.isContact11 = false;
    },
    /**
     * 碰撞发生时
     * tag:
     *     land-0
     *     wall-1
     *     table-2、3
     *     prop-4
     *     barrier-5
     *     tableTemp-10、11
     *     scoreProp-40
     *     energyProp-41
     *     mapUpProp-42
     *     speedProp-43
     *     jelly-66
     */
    onBeginContact: function (contact, selfCollider, otherCollider){
        if(selfCollider.tag==10) this.isContact10 = true;
        if(selfCollider.tag==11) this.isContact11 = true;
        //设置为红色状态
        this.node.color = cc.color(255,0,0);
        //0.01s内再次碰撞时取消标记为非碰撞状态
        if(this.t) clearTimeout(this.t);
        //标记为碰撞状态
        cc.find('Canvas').getComponent('game').isContact = true;
    },
    /**
     * 碰撞结束时
     */
    onEndContact: function (contact, selfCollider, otherCollider){
        if(selfCollider.tag==10) this.isContact10 = false;
        if(selfCollider.tag==11) this.isContact11 = false;

        let table = cc.find('Canvas').getComponent('game');
        //防止重复触发
        if(this.t) clearTimeout(this.t);
        //持续0.01s未再次碰撞标记为非碰撞状态
        this.t = setTimeout(()=>{
            //标记为非碰撞状态
            if(!this.isContact10&&!this.isContact11){
                table.isContact = false;
            }
        },10);
        //平台的两个碰撞面都脱离碰撞状态时
        if(!this.isContact10&&!this.isContact11){
            let len = cc.find('Canvas').getComponent('game').len;
            let minLen = cc.find('Canvas').getComponent('game').minLen;
            let maxLen = cc.find('Canvas').getComponent('game').maxLen;
            let currentEng = cc.find('Canvas').getComponent('game').currentEng;
            //当长度达标、能量条足够时取消红色状态
            if(len>=minLen&&len<=maxLen&&len<=currentEng){
                this.node.color = cc.color(255,255,255);
            }
        }
    }
});
