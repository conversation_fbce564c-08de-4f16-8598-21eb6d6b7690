var cloudData = require('cloudUrl');
var userData = require('userData');
cc.Class({
    extends: cc.Component,

    properties: {
        textButton: cc.Node,
        textButton2: cc.Node,
        land: cc.Node,
    },

    start(){
        if(CC_WECHATGAME){
            // 动态加载按钮贴图
            this.textButton.getComponent(cc.Button).normalSprite = new cc.SpriteFrame(cloudData.ccData.textButtonBg);
            this.textButton.getComponent(cc.Button).normalSprite.insetBottom = 34;
            this.textButton.getComponent(cc.Button).normalSprite.insetLeft = 41;
            this.textButton.getComponent(cc.Button).normalSprite.insetRight = 41;
            this.textButton.getComponent(cc.Button).normalSprite.insetTop = 34;

            this.textButton.getComponent(cc.Button).pressedSprite = new cc.SpriteFrame(cloudData.ccData.textButtonBgDark);
            this.textButton.getComponent(cc.Button).pressedSprite.insetBottom = 34;
            this.textButton.getComponent(cc.Button).pressedSprite.insetLeft = 41;
            this.textButton.getComponent(cc.Button).pressedSprite.insetRight = 41;
            this.textButton.getComponent(cc.Button).pressedSprite.insetTop = 34;

            if(this.textButton2){
                this.textButton2.getComponent(cc.Button).normalSprite = this.textButton.getComponent(cc.Button).normalSprite;
                this.textButton2.getComponent(cc.Button).pressedSprite = this.textButton.getComponent(cc.Button).pressedSprite;
            }
        }
    },

    closeWin(callback){
        this.node.getChildByName('textWinBg1').runAction(
            cc.spawn(
                cc.scaleTo(0.2,0.5,0.5),cc.fadeOut(0.2)
            )
        );
        this.node.getChildByName('grayBg').runAction(
            cc.sequence(
                cc.fadeOut(0.2),cc.callFunc(()=>{
                    this.node.active = false;
                    if(callback) callback();
                })
            )
        );
    },

    noButton(event,customEventData){
        if(CC_WECHATGAME){
            // 播放音效
            var audios = cc.find('audios').getComponent('audios');
            audios.playEffect(audios.effect_click);
        }
        if(customEventData=='gg'){
            this.closeWin(()=>{
                // 继续游戏
                cc.find('Canvas').getComponent('game').pauseGame();
                this.land.getComponent('land').jellyBoom();
            });
        }else{
            this.closeWin(()=>{
                // 继续游戏
                cc.find('Canvas').getComponent('game').pauseGame();
            });
        }
    },
    
    yesButton(event,customEventData){
        if(CC_WECHATGAME){
            // 播放音效
            var audios = cc.find('audios').getComponent('audios');
            audios.playEffect(audios.effect_click);
        }
        if(customEventData=='gg'){
            if(CC_WECHATGAME){
                // 创建激励视频广告实例，提前初始化
                let videoAd = wx.createRewardedVideoAd({
                    adUnitId: 'adunit-d2aa3d3e31159fd2'
                });
                // 用户触发广告后，显示激励视频广告
                videoAd.show().catch(err => {
                    // 失败重试
                    videoAd.load()
                        .then(() => videoAd.show())
                        .catch(err => {
                            console.log('重试失败',err)
                            this.closeWin(()=>{
                                // 继续游戏
                                cc.find('Canvas').getComponent('game').pauseGame();
                                this.land.getComponent('land').jellyBoom();
                            });
                        })
                });
                // 捕捉错误
                videoAd.onError(err => {
                    console.log('捕捉错误',err);
                    this.closeWin(()=>{
                        // 继续游戏
                        cc.find('Canvas').getComponent('game').pauseGame();
                        this.land.getComponent('land').jellyBoom();
                    });
                })
                videoAd.onClose(res => {
                    // 用户点击了【关闭广告】按钮
                    // 小于 2.1.0 的基础库版本，res 是一个 undefined
                    videoAd.offClose();
                    if (res && res.isEnded || res === undefined) {
                        // 正常播放结束，可以下发游戏奖励
                        console.log('正常播放结束')
                        this.closeWin(()=>{
                            // 复活游戏
                            cc.find('Canvas').getComponent('game').resurgenceGame();
                            // 继续游戏
                            cc.find('Canvas').getComponent('game').pauseGame();
                            // 碰撞标记
                            this.land.getComponent('land').isCrash = false;
                        });
                    }else {
                        // 播放中途退出，不下发游戏奖励
                        console.log('播放中途退出')
                        this.closeWin(()=>{
                            // 继续游戏
                            cc.find('Canvas').getComponent('game').pauseGame();
                            this.land.getComponent('land').jellyBoom();
                        });
                    }
                })
            }else{
                // 非微信复活测试
                this.closeWin(()=>{
                    // 复活游戏
                    cc.find('Canvas').getComponent('game').resurgenceGame();
                    // 继续游戏
                    cc.find('Canvas').getComponent('game').pauseGame();
                    // 碰撞标记
                    this.land.getComponent('land').isCrash = false;
                });
            }
        }else{
            // 结束游戏
            cc.find('Canvas').getComponent('game').endGame();
            this.closeWin(()=>{
                // 淡出
                cc.find('Canvas').runAction(
                    cc.sequence(
                        cc.fadeOut(0.5),cc.callFunc(()=>{
                            cc.director.loadScene("index");
                        })
                    )
                )
            });
        }
        
    },

    retweetingButton(){
        if(CC_WECHATGAME){
            // 播放音效
            var audios = cc.find('audios').getComponent('audios');
            audios.playEffect(audios.effect_click);
        }
        wx.shareAppMessage({
            title: '转发到群聊进入，查看群排行榜',
            imageUrl: cc.game.canvas.toTempFilePathSync({
                destWidth: 500,
                destHeight: 400
            })
        })
        this.closeWin();
    }

});
