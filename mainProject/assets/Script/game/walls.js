var cloudData = require('cloudUrl');
cc.Class({
    extends: cc.Component,

    properties: {
        leftWall: cc.Node,
        rightWall: cc.Node,
        jelly: cc.Node,
        leftWallContent: cc.Node,
        rightWallContent: cc.Node,
        leftLayout: cc.Node,
        rightLayout: cc.Node,
    },
	onLoad(){
        //初始化墙壁长宽
        this.initWalls();
    },
    update (dt) {
        // 墙壁跟随果冻垂直移动（移动单位为贴图长宽）
        this.leftWall.y = Math.floor(this.jelly.y/this.leftSpriteH)*this.leftSpriteH;
        this.rightWall.y = Math.floor(this.jelly.y/this.rightSpriteH)*this.rightSpriteH;
    },
    initWalls(){
        // 墙壁贴图高度
        this.leftSpriteH = this.leftWallContent.height*this.leftWallContent.scaleY;
        this.rightSpriteH = this.rightWallContent.height*this.rightWallContent.scaleY;
        // 设置墙壁节点宽度
        this.leftWall.width = cc.winSize.width/2;
        this.rightWall.width = cc.winSize.width/2;
        // 设置墙壁节点高度
        this.leftWall.height = cc.winSize.height+this.leftSpriteH*2;
        this.rightWall.height = cc.winSize.height+this.rightSpriteH*2;
        // 创建墙壁铺满可视区
        // 创建个数
        var num = Math.ceil(this.leftWall.height/this.leftSpriteH) - this.leftLayout.children.length;
        for(let i=0; i<num; i++){
            let leftWallContent = cc.instantiate(this.leftWallContent);
            this.leftLayout.addChild(leftWallContent);
        }
        // 创建个数
        var num2 =Math.ceil(this.rightWall.height/this.rightSpriteH) - this.rightLayout.children.length;
        for(let i=0; i<num2; i++){
            let rightWallContent = cc.instantiate(this.rightWallContent);
            this.rightLayout.addChild(rightWallContent);
        }

    },
});
