var cloudData = require('cloudUrl');
cc.Class({
    extends: cc.Component,

    properties: {
        content: cc.Node,
        pageList: cc.Node,
        pageNumLabel: cc.Label,
        gameCanvas: cc.Node,
        jellyFace: cc.Sprite
    },

    start(){
        this.content.scale = cc.winSize.width/this.content.width;
        if(CC_WECHATGAME){
            // 果冻表情(慌张,微笑,严肃,自信)
            this.faceList = [
                new cc.SpriteFrame(cloudData.ccData['jellyFace_HZ']),
                new cc.SpriteFrame(cloudData.ccData['jellyFace_WX']),
                new cc.SpriteFrame(cloudData.ccData['jellyFace_YS']),
                new cc.SpriteFrame(cloudData.ccData['jellyFace_ZX'])
            ];
        }
        // 当前页码
        this.currentPage = 0;
        this.changeFace();
    },

    closeButton(){
        if(CC_WECHATGAME){
            // 播放音效
            var audios = cc.find('audios').getComponent('audios');
            audios.playEffect(audios.effect_click);
        }
        this.node.getChildByName('helpWinContent').runAction(
            cc.spawn(
                cc.moveBy(0.2,0,-750),cc.fadeOut(0.2)
            )
        );
        this.node.getChildByName('grayBg').runAction(
            cc.sequence(
                cc.fadeOut(0.4),cc.callFunc(()=>{
                    this.node.active = false
                    // 如果是游戏页面则继续游戏
                    if(this.gameCanvas){
                        this.gameCanvas.getComponent('game').pauseGame();
                    }
                })
            )
        );
    },

    rightButton(){
        if(CC_WECHATGAME){
            // 播放音效
            var audios = cc.find('audios').getComponent('audios');
            audios.playEffect(audios.effect_click);
        }
        this.pageList.children[this.currentPage].active = false;
        this.currentPage = (this.currentPage+1)%this.pageList.children.length;
        this.pageList.children[this.currentPage].active = true;
        this.pageNumLabel.string = String(this.currentPage+1);
        this.changeFace();
        this.playVoi();
    },

    leftButton(){
        if(CC_WECHATGAME){
            // 播放音效
            var audios = cc.find('audios').getComponent('audios');
            audios.playEffect(audios.effect_click);
        }
        this.pageList.children[this.currentPage].active = false;
        this.currentPage = this.currentPage==0?this.pageList.children.length-1:this.currentPage-1;
        this.pageList.children[this.currentPage].active = true;
        this.pageNumLabel.string = String(this.currentPage+1);
        this.changeFace();
        this.playVoi();
    },

    changeFace(){
        if(CC_WECHATGAME){
            var ran = Math.floor(Math.random()*this.faceList.length);
            this.jellyFace.spriteFrame = this.faceList[ran];
        }
    },

    playVoi(){
        if(CC_WECHATGAME){
            // 随机播放语音
            var ranNum = Math.floor(Math.random()*5+1)
            var audios = cc.find('audios').getComponent('audios');
            audios.playVoi(audios['voi_wbbb'+ranNum]);
        }
    }
});
