cc.Class({
    extends: cc.Component,

    properties: {
    },

    playButton(){
        if(CC_WECHATGAME){
            // 播放音效
            var audios = cc.find('audios').getComponent('audios');
            audios.playEffect(audios.effect_play);
        }
        // 全部加载成功后切换场景
        // 淡出
            cc.find('Canvas').runAction(
                cc.sequence(
                    cc.fadeOut(0.5),cc.callFunc(()=>{
                        cc.director.loadScene("game");
                    })
                )
            )
    },

    skinButton(){
        if(CC_WECHATGAME){
            // 播放音效
            var audios = cc.find('audios').getComponent('audios');
            audios.playEffect(audios.effect_click);
        }
        // 全部加载成功后切换场景
        // 淡出
            cc.find('Canvas').runAction(
                cc.sequence(
                    cc.fadeOut(0.5),cc.callFunc(()=>{
                        cc.director.loadScene("skin");
                    })
                )
            )
    },

    topButton(){
        if(CC_WECHATGAME){
            // 播放音效
            var audios = cc.find('audios').getComponent('audios');
            audios.playEffect(audios.effect_click);
        }
        // 全部加载成功后切换场景
        // 淡出
            cc.find('Canvas').runAction(
                cc.sequence(
                    cc.fadeOut(0.5),cc.callFunc(()=>{
                        cc.director.loadScene("top");
                    })
                )
            )
    }
});
