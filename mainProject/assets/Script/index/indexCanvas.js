var cloudData = require('cloudUrl');
cc.Class({
    extends: cc.Component,

    properties: {
      
    },

    onload() {
        //当屏幕大小发生改变时调用
        cc.view.setResizeCallback(()=>{
            this.node.width = cc.winSize.width;
            this.node.height = cc.winSize.height;
        });
    },

    start(){
        if(CC_WECHATGAME){
            wx.showShareMenu({
                withShareTicket: true
            })
            wx.onShareAppMessage(()=>{
                return {
                    title: '群组排行榜你排第几名？',
                    imageUrl: cc.game.canvas.toTempFilePathSync({
                        destWidth: 500,
                        destHeight: 400
                    })
                }
            });
            // 播放背景音乐
            var audios = cc.find('audios').getComponent('audios');
            audios.changeBgm(audios.bgm1);
        }
    }
});
