cc.Class({
    extends: cc.Component,

    properties: {
        anim: cc.Animation,
        setWin: cc.Node,
        helpWin: cc.Node,
        continueWin: cc.Node,
        textWin: cc.Node,
        textWinLabel: cc.Label,
        textWinYes: cc.Button,
        textWinNo: cc.Button,
        gameCanvas: cc.Node
    },

    onLoad(){
        this.isOn = 0;
    },

    moreButton(event,customEventData){
        if(CC_WECHATGAME){
            // 播放音效
            var audios = cc.find('audios').getComponent('audios');
            audios.playEffect(audios.effect_click);
        }
        if(this.isOn){
            this.moreOff(customEventData);
        }else{
            this.moreOn(customEventData);
        }
    },

    moreOn(num){
        this.anim.play('moreButtonOn-'+num);
        this.isOn = 1;
    },

    moreOff(num){
        this.anim.play('moreButtonOff-'+num);
        this.isOn = 0
    },

    indexButton(type){
        if(CC_WECHATGAME){
            // 播放音效
            var audios = cc.find('audios').getComponent('audios');
            audios.playEffect(audios.effect_click);
        }
        // 暂停游戏
        this.gameCanvas.getComponent('game').pauseGame();
        // 文字弹窗
        this.textWin.active = true;
        if(type=='gg'){
            this.textWinLabel.string = '是否观看广告来复活\n（6-15秒视频）';
            this.textWinYes.clickEvents[0].customEventData = 'gg';
            this.textWinNo.clickEvents[0].customEventData = 'gg';
        }else{
            this.textWinLabel.string = '是否放弃本局游戏\n（不结算分数及奖励）';
            this.textWinYes.clickEvents[0].customEventData = 'index';
            this.textWinNo.clickEvents[0].customEventData = 'index';
        }
        
        this.textWin.getChildByName('textWinBg1').runAction(
            cc.spawn(
                cc.scaleTo(0.2,1,1),cc.fadeIn(0.3)
            )
        );
        this.textWin.getChildByName('grayBg').runAction(cc.fadeTo(0.2,80));
    },

    pauseButton(){
        if(CC_WECHATGAME){
            // 播放音效
            var audios = cc.find('audios').getComponent('audios');
            audios.playEffect(audios.effect_click);
        }
        // 暂停游戏
        this.gameCanvas.getComponent('game').pauseGame();

        this.continueWin.active = true;
        this.continueWin.getChildByName('continueButton').runAction(
            cc.spawn(
                cc.scaleTo(0.2,1.8,1.8),cc.fadeIn(0.3)
            )
        );
        this.continueWin.getChildByName('grayBg').runAction(cc.fadeTo(0.2,80));
    },

    setButton(){
        if(CC_WECHATGAME){
            // 播放音效
            var audios = cc.find('audios').getComponent('audios');
            audios.playEffect(audios.effect_click);
        }
        // 如果是游戏页面则暂停游戏
        if(this.gameCanvas){
            this.gameCanvas.getComponent('game').pauseGame();
        }
        this.setWin.active = true;
        this.setWin.getChildByName('setWinBg').runAction(
            cc.spawn(
                cc.scaleTo(0.2,1.8,1.8),cc.fadeIn(0.3)
            )
        );
        this.setWin.getChildByName('grayBg').runAction(cc.fadeTo(0.2,80));
    },

    helpButton(){
        if(CC_WECHATGAME){
            // 播放音效
            var audios = cc.find('audios').getComponent('audios');
            audios.playEffect(audios.effect_click);
            this.helpWin.getComponent('helpWin').playVoi();
        }
        // 如果是游戏页面则暂停游戏
        if(this.gameCanvas){
            this.gameCanvas.getComponent('game').pauseGame();
        }
        this.helpWin.active = true;
        this.helpWin.getChildByName('helpWinContent').runAction(
            cc.spawn(
                cc.moveBy(0.3,0,750),cc.fadeIn(0.3)
            )
        );
        this.helpWin.getChildByName('grayBg').runAction(cc.fadeTo(0.2,80));
    },

    closeButton(){
        if(CC_WECHATGAME){
            // 播放音效
            var audios = cc.find('audios').getComponent('audios');
            audios.playEffect(audios.effect_click);
        }
        this.setWin.getChildByName('setWinBg').runAction(
            cc.spawn(
                cc.scaleTo(0.3,1,1),cc.fadeOut(0.2)
            )
        );
        this.setWin.getChildByName('grayBg').runAction(
            cc.sequence(
                cc.fadeOut(0.4),cc.callFunc(()=>{
                    this.setWin.active = false
                    // 如果是游戏页面则继续游戏
                    if(this.gameCanvas){
                        this.gameCanvas.getComponent('game').pauseGame();
                    }
                })
            )
        );
    },

    continueButton(){
        if(CC_WECHATGAME){
            // 播放音效
            var audios = cc.find('audios').getComponent('audios');
            audios.playEffect(audios.effect_click);
        }
        this.continueWin.getChildByName('continueButton').runAction(
            cc.spawn(
                cc.scaleTo(0.3,0.5,0.5),cc.fadeOut(0.2)
            )
        );
        this.continueWin.getChildByName('grayBg').runAction(
            cc.sequence(
                cc.fadeOut(0.3),cc.callFunc(()=>{
                    this.continueWin.active = false
                    // 继续游戏
                    this.gameCanvas.getComponent('game').pauseGame();
                })
            )
        );
    },

    textButton(event,customEventData){
        if(CC_WECHATGAME){
            // 播放音效
            var audios = cc.find('audios').getComponent('audios');
            audios.playEffect(audios.effect_click);
        }
        console.log(event,customEventData)
    },

    sliderHandle(){
        if(CC_WECHATGAME){
            // 播放音效
            var audios = cc.find('audios').getComponent('audios');
            audios.playEffect(audios.effect_click);
        }
    }
});
