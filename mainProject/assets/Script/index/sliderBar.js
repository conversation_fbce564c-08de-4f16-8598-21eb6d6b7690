var userData = require('userData');
cc.Class({
    extends: cc.Component,

    properties: {
        sliderBar: cc.Node,
        sliderHandle: cc.Node
    },

    start(){
        if(this.node.parent.name == 'bgmVol'){
            this.getComponent(cc.Slider).progress = userData.bgmVol;
        }else if(this.node.parent.name == 'effectVol'){
            this.getComponent(cc.Slider).progress = userData.effectVol;
        }
        this.sliderBar.width = this.sliderHandle.x+150;
    },

    slideEvent(){
        this.sliderBar.width = this.sliderHandle.x+150;
        // 保存音量到公共变量
        userData[this.node.parent.name] = this.getComponent(cc.Slider).progress;
        if(this.node.parent.name == 'bgmVol'){
            // 调整音量背景音乐
            var audios = cc.find('audios').getComponent('audios');
            audios.changeVol();
        }
    }
});
