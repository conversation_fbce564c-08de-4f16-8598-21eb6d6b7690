var cloudData = require('cloudUrl');
var userData = require('userData');
const fileList = require('cloudUrl').fileList;
cc.Class({
    extends: cc.Component,

    properties: {
        bar: cc.ProgressBar
    },
    // 放于加载资源页
    onLoad(){
        if(CC_WECHATGAME){
            let {screenWidth,screenHeight} = wx.getSystemInfoSync();
            // 创建 Banner 广告实例，提前初始化
            this.bannerAd = wx.createBannerAd({
                adUnitId: 'adunit-16b06e4f7f6d8a9a',
                style: {
                    left: screenWidth/2-150,
                    top: screenHeight-100,
                    width: 300
                }
            })
            this.bannerAd.onError(err=>{
                console.log(err)
            })
            // 加载资源时显示 Banner 广告
            this.bannerAd.show().then(()=>console.log('banner 广告显示'));
        }
        cloudData.url = {};
        cloudData.ccData = {};
        // 当前下载进度
        this.currentProg = 0;
        // 预下载的文件列表
        this.downList = [];
        // 当前正在下载的文件个数
        this.downNum = 0;
        // 用户信息是否获得
        this.isGetData = false;
        if(CC_WECHATGAME){
            // 初始化云开发
            wx.cloud.init();
            
            // 调用云函数获取用户皮肤数据
            wx.cloud.callFunction({
                name:'getSkin',
                complete: res=>{
                    // 存储到全局变量
                    userData.skin.skinChip =  res.result.skinChip;
                    userData.skin.skinLock =  res.result.skinLock;
                    userData.skin.currentSkin =  res.result.currentSkin;
                    // 用户信息是否获得
                    this.isGetData = true;
                    // 全部加载成功后切换场景
                    this.switchScene();
                }
            })

            // 下载云存储资源
            for(let file of fileList){
                // 检查是否有本地资源
                wx.getFileSystemManager().readFile({
                    filePath: wx.env.USER_DATA_PATH+'/'+file.name+'.'+file.type,
                    success: res =>{
                        // 将本地文件路径存储到全局变量
                        cloudData.url[file.name] = wx.env.USER_DATA_PATH+'/'+file.name+'.'+file.type;
                        // 加载资源
                        this.load(file.name,file.type,file.id)
                    },
                    fail: err =>{
                        console.log('本地无'+file.name+'资源')
                        // 添加到预下载列表
                        this.downList.push(file);
                    }
                });
            }
        }   
    },
    update(){
        this.readyDown();
    },
    /**
     * 下载预下载列表的文件资源
     */
    readyDown(){
        // 若当前正在下载的资源个数小于10个，且有预下载资源，则进行下载
        if(this.downList.length>0 && this.downNum<10){
            // 当前正在下载的文件个数
            this.downNum += 1;
            // 下载预下载列表第一个资源
            this.down(this.downList[0].name,this.downList[0].type,this.downList[0].id);
            this.downList.shift();
        }
    },
    /**
     * 下载资源
     */
    down(name,type,id){
        console.log('开始下载'+name+'资源')
        wx.cloud.downloadFile({
            fileID: id,
            success: res => {
                // 保存资源到本地
                wx.getFileSystemManager().saveFile({
                    tempFilePath: res.tempFilePath,
                    filePath: wx.env.USER_DATA_PATH+'/'+name+'.'+type,
                    success: res =>{
                        // 将本地文件路径存储到全局变量
                        cloudData.url[name] = wx.env.USER_DATA_PATH+'/'+name+'.'+type;
                        // 加载资源
                        this.load(name,type,id);
                        // 当前正在下载的文件个数
                        this.downNum -= 1;
                    }
                });
            },
            fail: err => {
                console.log('下载'+name+'资源超时,正在重新下载');
                // 重新下载资源
                this.down(name,type,id);
            }
        })
    },
    /**
     * 加载资源
     */
    load(name,type,id){
        cc.loader.load(wx.env.USER_DATA_PATH+'/'+name+'.'+type,(err,ccData)=>{
            if(err){
                console.log('加载资源错误：'+name);
                // 重新下载资源
                this.down(name,type,id);
                return;
            }
            // 将加载完的资源存储到全局变量
            cloudData.ccData[name] = ccData;
            // 更新进度
            this.currentProg += 1;
            this.bar.progress = 0.1+(this.currentProg/fileList.length)*0.9;
            // 全部加载成功后切换场景
            this.switchScene();
        });
    },
    /**
     * 全部加载成功后切换场景
     */
    switchScene(){
        // 资源加载完成,用户数据获取完成
        if(this.currentProg == fileList.length && this.isGetData){
            console.log('游戏资源与用户数据获取完成！')
            // 子域加载图片
            wx.postMessage({
                msg: 'loadImg',
                topList: cloudData.url.topList,
                firstIcon: cloudData.url.firstIcon,
                secondIcon: cloudData.url.secondIcon,
                thirdIcon: cloudData.url.thirdIcon,
                scoreIcon: cloudData.url.scoreIcon,
                heightIcon: cloudData.url.heightIcon
            });
            if(CC_WECHATGAME){
                // 加载资源完成后隐藏 Banner 广告
                this.bannerAd.hide();
            }
            // 淡出
            cc.find('Canvas').runAction(
                cc.sequence(
                    cc.fadeOut(0.5),cc.callFunc(()=>{
                        // 切换场景
                        cc.director.loadScene("index");
                    })
                )
            )
        }
    }
});
