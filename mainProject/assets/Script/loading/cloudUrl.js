module.exports = {
	url: {},
	ccData:{},
	fileList: [
		{
			name: 'bgm1',
			type: 'mp3',
			id: 'cloud://cloud-i9tsg.636c-cloud-i9tsg-1302201070/audios/bgm1_x2.mp3'
		},
		{
			name: 'bgm2',
			type: 'mp3',
			id: 'cloud://cloud-i9tsg.636c-cloud-i9tsg-1302201070/audios/bgm2_x2.mp3'
		},
		{
			name: 'bgm3',
			type: 'mp3',
			id: 'cloud://cloud-i9tsg.636c-cloud-i9tsg-1302201070/audios/bgm3_x1.mp3'
		},
		{
			name: 'effect_click',
			type: 'mp3',
			id: 'cloud://cloud-i9tsg.636c-cloud-i9tsg-1302201070/audios/effect_click.mp3'
		},
		{
			name: 'effect_play',
			type: 'mp3',
			id: 'cloud://cloud-i9tsg.636c-cloud-i9tsg-1302201070/audios/effect_play.mp3'
		},
		{
			name: 'effect_jump',
			type: 'mp3',
			id: 'cloud://cloud-i9tsg.636c-cloud-i9tsg-1302201070/audios/effect_jump.mp3'
		},
		{
			name: 'voi_wbbb1',
			type: 'mp3',
			id: 'cloud://cloud-i9tsg.636c-cloud-i9tsg-1302201070/audios/voi_wbbb1.mp3'
		},
		{
			name: 'voi_wbbb2',
			type: 'mp3',
			id: 'cloud://cloud-i9tsg.636c-cloud-i9tsg-1302201070/audios/voi_wbbb2.mp3'
		},
		{
			name: 'voi_wbbb3',
			type: 'mp3',
			id: 'cloud://cloud-i9tsg.636c-cloud-i9tsg-1302201070/audios/voi_wbbb3.mp3'
		},
		{
			name: 'voi_wbbb4',
			type: 'mp3',
			id: 'cloud://cloud-i9tsg.636c-cloud-i9tsg-1302201070/audios/voi_wbbb4.mp3'
		},
		{
			name: 'voi_wbbb5',
			type: 'mp3',
			id: 'cloud://cloud-i9tsg.636c-cloud-i9tsg-1302201070/audios/voi_wbbb5.mp3'
		},
		{
			name: 'effect_died',
			type: 'mp3',
			id: 'cloud://cloud-i9tsg.636c-cloud-i9tsg-1302201070/audios/effect_died.mp3'
		},
		{
			name: 'fontHWHP',
			type: 'ttf',
			id: 'cloud://cloud-i9tsg.636c-cloud-i9tsg-1302201070/font/华文琥珀.ttf'
		},
		{
			name: 'fontFZYY',
			type: 'TTF',
			id: 'cloud://cloud-i9tsg.636c-cloud-i9tsg-1302201070/font/幼圆.TTF'
		},
		// {
		// 	name: 'map',
		// 	type: 'png',
		// 	id: 'cloud://cloud-i9tsg.636c-cloud-i9tsg-1302201070/images/tx.png'
		// },
		// {
		// 	name: 'land',
		// 	type: 'png',
		// 	id: 'cloud://cloud-i9tsg.636c-cloud-i9tsg-1302201070/images/default_toggle_pressed.png'
		// },
		// {
		// 	name: 'wall',
		// 	type: 'png',
		// 	id: 'cloud://cloud-i9tsg.636c-cloud-i9tsg-1302201070/images/default_sprite.png'
		// },
		// {
		// 	name: 'jelly',
		// 	type: 'png',
		// 	id: 'cloud://cloud-i9tsg.636c-cloud-i9tsg-1302201070/images/default_radio_button_off.png'
		// },
		// {
		// 	name: 'table',
		// 	type: 'png',
		// 	id: 'cloud://cloud-i9tsg.636c-cloud-i9tsg-1302201070/images/barrier.png'
		// },
		// {
		// 	name: 'barrier',
		// 	type: 'png',
		// 	id: 'cloud://cloud-i9tsg.636c-cloud-i9tsg-1302201070/images/barrier.png'
		// },
		// {
		// 	name: 'prop',
		// 	type: 'png',
		// 	id: 'cloud://cloud-i9tsg.636c-cloud-i9tsg-1302201070/images/default_radio_button_on.png'
		// },
		{
			name: 'sky',
			type: 'png',
			id: 'cloud://cloud-i9tsg.636c-cloud-i9tsg-1302201070/images/index/天空.png'
		},
		{
			name: 'cloud',
			type: 'png',
			id: 'cloud://cloud-i9tsg.636c-cloud-i9tsg-1302201070/images/index/云.png'
		},
		{
			name: 'grass',
			type: 'png',
			id: 'cloud://cloud-i9tsg.636c-cloud-i9tsg-1302201070/images/index/草地.png'
		},
		{
			name: 'leftJelly',
			type: 'png',
			id: 'cloud://cloud-i9tsg.636c-cloud-i9tsg-1302201070/images/index/前景左.png'
		},
		{
			name: 'rightJelly',
			type: 'png',
			id: 'cloud://cloud-i9tsg.636c-cloud-i9tsg-1302201070/images/index/前景右.png'
		},
		{
			name: 'leftBalloon',
			type: 'png',
			id: 'cloud://cloud-i9tsg.636c-cloud-i9tsg-1302201070/images/index/小鸡.png'
		},
		{
			name: 'rightBalloon',
			type: 'png',
			id: 'cloud://cloud-i9tsg.636c-cloud-i9tsg-1302201070/images/index/气球.png'
		},
		{
			name: 'title',
			type: 'png',
			id: 'cloud://cloud-i9tsg.636c-cloud-i9tsg-1302201070/images/index/游戏标题.png'
		},
		{
			name: 'titleJelly',
			type: 'png',
			id: 'cloud://cloud-i9tsg.636c-cloud-i9tsg-1302201070/images/index/果冻.png'
		},
		{
			name: 'topButtonBg',
			type: 'png',
			id: 'cloud://cloud-i9tsg.636c-cloud-i9tsg-1302201070/images/index/排行榜.png'
		},
		{
			name: 'playButtonBg',
			type: 'png',
			id: 'cloud://cloud-i9tsg.636c-cloud-i9tsg-1302201070/images/index/开始游戏.png'
		},
		{
			name: 'skinButtonBg',
			type: 'png',
			id: 'cloud://cloud-i9tsg.636c-cloud-i9tsg-1302201070/images/index/皮肤.png'
		},
		{
			name: 'moreBg',
			type: 'png',
			id: 'cloud://cloud-i9tsg.636c-cloud-i9tsg-1302201070/images/index/透明圈.png'
		},
		{
			name: 'helpButtonBg',
			type: 'png',
			id: 'cloud://cloud-i9tsg.636c-cloud-i9tsg-1302201070/images/index/问号.png'
		},
		{
			name: 'setButtonBg',
			type: 'png',
			id: 'cloud://cloud-i9tsg.636c-cloud-i9tsg-1302201070/images/index/设置.png'
		},
		{
			name: 'moreButtonBg',
			type: 'png',
			id: 'cloud://cloud-i9tsg.636c-cloud-i9tsg-1302201070/images/index/更多.png'
		},
		{
			name: 'scoreProp',
			type: 'png',
			id: 'cloud://cloud-i9tsg.636c-cloud-i9tsg-1302201070/images/game/prop/scoreProp.png'
		},
		{
			name: 'energyProp',
			type: 'png',
			id: 'cloud://cloud-i9tsg.636c-cloud-i9tsg-1302201070/images/game/prop/energyProp.png'
		},
		{
			name: 'mapUpProp',
			type: 'png',
			id: 'cloud://cloud-i9tsg.636c-cloud-i9tsg-1302201070/images/game/prop/mapUpProp.png'
		},
		{
			name: 'speedProp',
			type: 'png',
			id: 'cloud://cloud-i9tsg.636c-cloud-i9tsg-1302201070/images/game/prop/speedProp.png'
		},
		{
			name: 'backButtonBg',
			type: 'png',
			id: 'cloud://cloud-i9tsg.636c-cloud-i9tsg-1302201070/images/skin/backButtonBg.png'
		},
		{
			name: 'backButtonBgDark',
			type: 'png',
			id: 'cloud://cloud-i9tsg.636c-cloud-i9tsg-1302201070/images/skin/backButtonBgDark.png'
		},
		{
			name: 'textButtonBg',
			type: 'png',
			id: 'cloud://cloud-i9tsg.636c-cloud-i9tsg-1302201070/images/skin/textButtonBg.png'
		},
		{
			name: 'textButtonBgDark',
			type: 'png',
			id: 'cloud://cloud-i9tsg.636c-cloud-i9tsg-1302201070/images/skin/textButtonBgDark.png'
		},
		{
			name: 'blueTL',
			type: 'png',
			id: 'cloud://cloud-i9tsg.636c-cloud-i9tsg-1302201070/images/skin/blueTL.png'
		},
		{
			name: 'blueTR',
			type: 'png',
			id: 'cloud://cloud-i9tsg.636c-cloud-i9tsg-1302201070/images/skin/blueTR.png'
		},
		{
			name: 'blueBL',
			type: 'png',
			id: 'cloud://cloud-i9tsg.636c-cloud-i9tsg-1302201070/images/skin/blueBL.png'
		},
		{
			name: 'blueBR',
			type: 'png',
			id: 'cloud://cloud-i9tsg.636c-cloud-i9tsg-1302201070/images/skin/blueBR.png'
		},
		{
			name: 'appleTL',
			type: 'png',
			id: 'cloud://cloud-i9tsg.636c-cloud-i9tsg-1302201070/images/skin/appleTL.png'
		},
		{
			name: 'appleTR',
			type: 'png',
			id: 'cloud://cloud-i9tsg.636c-cloud-i9tsg-1302201070/images/skin/appleTR.png'
		},
		{
			name: 'appleBL',
			type: 'png',
			id: 'cloud://cloud-i9tsg.636c-cloud-i9tsg-1302201070/images/skin/appleBL.png'
		},
		{
			name: 'appleBR',
			type: 'png',
			id: 'cloud://cloud-i9tsg.636c-cloud-i9tsg-1302201070/images/skin/appleBR.png'
		},
		{
			name: 'orangeTL',
			type: 'png',
			id: 'cloud://cloud-i9tsg.636c-cloud-i9tsg-1302201070/images/skin/orangeTL.png'
		},
		{
			name: 'orangeTR',
			type: 'png',
			id: 'cloud://cloud-i9tsg.636c-cloud-i9tsg-1302201070/images/skin/orangeTR.png'
		},
		{
			name: 'orangeBL',
			type: 'png',
			id: 'cloud://cloud-i9tsg.636c-cloud-i9tsg-1302201070/images/skin/orangeBL.png'
		},
		{
			name: 'orangeBR',
			type: 'png',
			id: 'cloud://cloud-i9tsg.636c-cloud-i9tsg-1302201070/images/skin/orangeBR.png'
		},
		{
			name: 'melonTL',
			type: 'png',
			id: 'cloud://cloud-i9tsg.636c-cloud-i9tsg-1302201070/images/skin/melonTL.png'
		},
		{
			name: 'melonTR',
			type: 'png',
			id: 'cloud://cloud-i9tsg.636c-cloud-i9tsg-1302201070/images/skin/melonTR.png'
		},
		{
			name: 'melonBL',
			type: 'png',
			id: 'cloud://cloud-i9tsg.636c-cloud-i9tsg-1302201070/images/skin/melonBL.png'
		},
		{
			name: 'melonBR',
			type: 'png',
			id: 'cloud://cloud-i9tsg.636c-cloud-i9tsg-1302201070/images/skin/melonBR.png'
		},
		{
			name: 'berriesTL',
			type: 'png',
			id: 'cloud://cloud-i9tsg.636c-cloud-i9tsg-1302201070/images/skin/berriesTL.png'
		},
		{
			name: 'berriesTR',
			type: 'png',
			id: 'cloud://cloud-i9tsg.636c-cloud-i9tsg-1302201070/images/skin/berriesTR.png'
		},
		{
			name: 'berriesBL',
			type: 'png',
			id: 'cloud://cloud-i9tsg.636c-cloud-i9tsg-1302201070/images/skin/berriesBL.png'
		},
		{
			name: 'berriesBR',
			type: 'png',
			id: 'cloud://cloud-i9tsg.636c-cloud-i9tsg-1302201070/images/skin/berriesBR.png'
		},
		{
			name: 'peachTL',
			type: 'png',
			id: 'cloud://cloud-i9tsg.636c-cloud-i9tsg-1302201070/images/skin/peachTL.png'
		},
		{
			name: 'peachTR',
			type: 'png',
			id: 'cloud://cloud-i9tsg.636c-cloud-i9tsg-1302201070/images/skin/peachTR.png'
		},
		{
			name: 'peachBL',
			type: 'png',
			id: 'cloud://cloud-i9tsg.636c-cloud-i9tsg-1302201070/images/skin/peachBL.png'
		},
		{
			name: 'peachBR',
			type: 'png',
			id: 'cloud://cloud-i9tsg.636c-cloud-i9tsg-1302201070/images/skin/peachBR.png'
		},
		{
			name: 'border',
			type: 'png',
			id: 'cloud://cloud-i9tsg.636c-cloud-i9tsg-1302201070/images/skin/border.png'
		},
		{
			name: 'blueButtonBg',
			type: 'png',
			id: 'cloud://cloud-i9tsg.636c-cloud-i9tsg-1302201070/images/skin/sideButton/blueButtonBg.png'
		},
		{
			name: 'appleButtonBg',
			type: 'png',
			id: 'cloud://cloud-i9tsg.636c-cloud-i9tsg-1302201070/images/skin/sideButton/appleButtonBg.png'
		},
		{
			name: 'orangeButtonBg',
			type: 'png',
			id: 'cloud://cloud-i9tsg.636c-cloud-i9tsg-1302201070/images/skin/sideButton/orangeButtonBg.png'
		},
		{
			name: 'melonButtonBg',
			type: 'png',
			id: 'cloud://cloud-i9tsg.636c-cloud-i9tsg-1302201070/images/skin/sideButton/melonButtonBg.png'
		},
		{
			name: 'berriesButtonBg',
			type: 'png',
			id: 'cloud://cloud-i9tsg.636c-cloud-i9tsg-1302201070/images/skin/sideButton/berriesButtonBg.png'
		},
		{
			name: 'peachButtonBg',
			type: 'png',
			id: 'cloud://cloud-i9tsg.636c-cloud-i9tsg-1302201070/images/skin/sideButton/peachButtonBg.png'
		},
		{
			name: 'buttonShadow',
			type: 'png',
			id: 'cloud://cloud-i9tsg.636c-cloud-i9tsg-1302201070/images/skin/sideButton/buttonShadow.png'
		},
		{
			name: 'lock',
			type: 'png',
			id: 'cloud://cloud-i9tsg.636c-cloud-i9tsg-1302201070/images/skin/sideButton/lock.png'
		},
		{
			name: 'heightButtonBg',
			type: 'png',
			id: 'cloud://cloud-i9tsg.636c-cloud-i9tsg-1302201070/images/top/heightButtonBg.png'
		},
		{
			name: 'heightButtonBgDark',
			type: 'png',
			id: 'cloud://cloud-i9tsg.636c-cloud-i9tsg-1302201070/images/top/heightButtonBgDark.png'
		},
		{
			name: 'scoreButtonBg',
			type: 'png',
			id: 'cloud://cloud-i9tsg.636c-cloud-i9tsg-1302201070/images/top/scoreButtonBg.png'
		},
		{
			name: 'scoreButtonBgDark',
			type: 'png',
			id: 'cloud://cloud-i9tsg.636c-cloud-i9tsg-1302201070/images/top/scoreButtonBgDark.png'
		},
		{
			name: 'topList',
			type: 'png',
			id: 'cloud://cloud-i9tsg.636c-cloud-i9tsg-1302201070/images/top/topList.png'
		},
		{
			name: 'topListBg',
			type: 'png',
			id: 'cloud://cloud-i9tsg.636c-cloud-i9tsg-1302201070/images/top/topListBg.png'
		},
		{
			name: 'topListBottom',
			type: 'png',
			id: 'cloud://cloud-i9tsg.636c-cloud-i9tsg-1302201070/images/top/topListBottom.png'
		},
		{
			name: 'firstIcon',
			type: 'png',
			id: 'cloud://cloud-i9tsg.636c-cloud-i9tsg-1302201070/images/top/firstIcon.png'
		},
		{
			name: 'secondIcon',
			type: 'png',
			id: 'cloud://cloud-i9tsg.636c-cloud-i9tsg-1302201070/images/top/secondIcon.png'
		},
		{
			name: 'thirdIcon',
			type: 'png',
			id: 'cloud://cloud-i9tsg.636c-cloud-i9tsg-1302201070/images/top/thirdIcon.png'
		},
		{
			name: 'heightIcon',
			type: 'png',
			id: 'cloud://cloud-i9tsg.636c-cloud-i9tsg-1302201070/images/top/heightIcon.png'
		},
		{
			name: 'scoreIcon',
			type: 'png',
			id: 'cloud://cloud-i9tsg.636c-cloud-i9tsg-1302201070/images/top/scoreIcon.png'
		},
		{
			name: 'setWinBg',
			type: 'png',
			id: 'cloud://cloud-i9tsg.636c-cloud-i9tsg-1302201070/images/index/setWin/setWinBg.png'
		},
		{
			name: 'sliderBg',
			type: 'png',
			id: 'cloud://cloud-i9tsg.636c-cloud-i9tsg-1302201070/images/index/setWin/sliderBg.png'
		},
		{
			name: 'sliderBar',
			type: 'png',
			id: 'cloud://cloud-i9tsg.636c-cloud-i9tsg-1302201070/images/index/setWin/sliderBar.png'
		},
		{
			name: 'sliderHandle',
			type: 'png',
			id: 'cloud://cloud-i9tsg.636c-cloud-i9tsg-1302201070/images/index/setWin/sliderHandle.png'
		},
		{
			name: 'closeButtonBg',
			type: 'png',
			id: 'cloud://cloud-i9tsg.636c-cloud-i9tsg-1302201070/images/index/setWin/closeButtonBg.png'
		},
		{
			name: 'landBg',
			type: 'png',
			id: 'cloud://cloud-i9tsg.636c-cloud-i9tsg-1302201070/images/game/landBg.png'
		},
		{
			name: 'landOutBg',
			type: 'png',
			id: 'cloud://cloud-i9tsg.636c-cloud-i9tsg-1302201070/images/game/landOutBg.png'
		},
		{
			name: 'wallBg',
			type: 'png',
			id: 'cloud://cloud-i9tsg.636c-cloud-i9tsg-1302201070/images/game/wallBg.png'
		},
		{
			name: 'wallOutBg',
			type: 'png',
			id: 'cloud://cloud-i9tsg.636c-cloud-i9tsg-1302201070/images/game/wallOutBg.png'
		},
		{
			name: 'mapBg',
			type: 'png',
			id: 'cloud://cloud-i9tsg.636c-cloud-i9tsg-1302201070/images/game/mapBg.png'
		},
		{
			name: 'nearVision1',
			type: 'png',
			id: 'cloud://cloud-i9tsg.636c-cloud-i9tsg-1302201070/images/game/nearVision1.png'
		},
		{
			name: 'nearVision2',
			type: 'png',
			id: 'cloud://cloud-i9tsg.636c-cloud-i9tsg-1302201070/images/game/nearVision2.png'
		},
		{
			name: 'nearVision3',
			type: 'png',
			id: 'cloud://cloud-i9tsg.636c-cloud-i9tsg-1302201070/images/game/nearVision3.png'
		},
		{
			name: 'nearVision4',
			type: 'png',
			id: 'cloud://cloud-i9tsg.636c-cloud-i9tsg-1302201070/images/game/nearVision4.png'
		},
		{
			name: 'distantVision1',
			type: 'png',
			id: 'cloud://cloud-i9tsg.636c-cloud-i9tsg-1302201070/images/game/distantVision1.png'
		},
		{
			name: 'distantVision2',
			type: 'png',
			id: 'cloud://cloud-i9tsg.636c-cloud-i9tsg-1302201070/images/game/distantVision2.png'
		},
		{
			name: 'distantVision3',
			type: 'png',
			id: 'cloud://cloud-i9tsg.636c-cloud-i9tsg-1302201070/images/game/distantVision3.png'
		},
		{
			name: 'distantVision4',
			type: 'png',
			id: 'cloud://cloud-i9tsg.636c-cloud-i9tsg-1302201070/images/game/distantVision4.png'
		},
		{
			name: 'distantVision5',
			type: 'png',
			id: 'cloud://cloud-i9tsg.636c-cloud-i9tsg-1302201070/images/game/distantVision5.png'
		},
		{
			name: 'distantVision6',
			type: 'png',
			id: 'cloud://cloud-i9tsg.636c-cloud-i9tsg-1302201070/images/game/distantVision6.png'
		},
		{
			name: 'distantVision7',
			type: 'png',
			id: 'cloud://cloud-i9tsg.636c-cloud-i9tsg-1302201070/images/game/distantVision7.png'
		},
		{
			name: 'blueJelly',
			type: 'png',
			id: 'cloud://cloud-i9tsg.636c-cloud-i9tsg-1302201070/images/game/jelly/blueJelly.png'
		},
		{
			name: 'appleJelly',
			type: 'png',
			id: 'cloud://cloud-i9tsg.636c-cloud-i9tsg-1302201070/images/game/jelly/appleJelly.png'
		},
		{
			name: 'orangeJelly',
			type: 'png',
			id: 'cloud://cloud-i9tsg.636c-cloud-i9tsg-1302201070/images/game/jelly/orangeJelly.png'
		},
		{
			name: 'melonJelly',
			type: 'png',
			id: 'cloud://cloud-i9tsg.636c-cloud-i9tsg-1302201070/images/game/jelly/melonJelly.png'
		},
		{
			name: 'berriesJelly',
			type: 'png',
			id: 'cloud://cloud-i9tsg.636c-cloud-i9tsg-1302201070/images/game/jelly/berriesJelly.png'
		},
		{
			name: 'peachJelly',
			type: 'png',
			id: 'cloud://cloud-i9tsg.636c-cloud-i9tsg-1302201070/images/game/jelly/peachJelly.png'
		},
		{
			name: 'blueTable',
			type: 'png',
			id: 'cloud://cloud-i9tsg.636c-cloud-i9tsg-1302201070/images/game/table/blueTable.png'
		},
		{
			name: 'appleTable',
			type: 'png',
			id: 'cloud://cloud-i9tsg.636c-cloud-i9tsg-1302201070/images/game/table/appleTable.png'
		},
		{
			name: 'orangeTable',
			type: 'png',
			id: 'cloud://cloud-i9tsg.636c-cloud-i9tsg-1302201070/images/game/table/orangeTable.png'
		},
		{
			name: 'melonTable',
			type: 'png',
			id: 'cloud://cloud-i9tsg.636c-cloud-i9tsg-1302201070/images/game/table/melonTable.png'
		},
		{
			name: 'berriesTable',
			type: 'png',
			id: 'cloud://cloud-i9tsg.636c-cloud-i9tsg-1302201070/images/game/table/berriesTable.png'
		},
		{
			name: 'peachTable',
			type: 'png',
			id: 'cloud://cloud-i9tsg.636c-cloud-i9tsg-1302201070/images/game/table/peachTable.png'
		},
		{
			name: 'yellowBarrier',
			type: 'png',
			id: 'cloud://cloud-i9tsg.636c-cloud-i9tsg-1302201070/images/game/barrier/yellowBarrier.png'
		},
		{
			name: 'redBarrier',
			type: 'png',
			id: 'cloud://cloud-i9tsg.636c-cloud-i9tsg-1302201070/images/game/barrier/redBarrier.png'
		},
		{
			name: 'jellyBarrier',
			type: 'png',
			id: 'cloud://cloud-i9tsg.636c-cloud-i9tsg-1302201070/images/game/barrier/jellyBarrier.png'
		},
		{
			name: 'energyBg',
			type: 'png',
			id: 'cloud://cloud-i9tsg.636c-cloud-i9tsg-1302201070/images/game/energyBg.png'
		},
		{
			name: 'energyBar',
			type: 'png',
			id: 'cloud://cloud-i9tsg.636c-cloud-i9tsg-1302201070/images/game/energyBar.png'
		},
		{
			name: 'dataBg',
			type: 'png',
			id: 'cloud://cloud-i9tsg.636c-cloud-i9tsg-1302201070/images/game/dataBg.png'
		},
		{
			name: 'currentHeightIcon',
			type: 'png',
			id: 'cloud://cloud-i9tsg.636c-cloud-i9tsg-1302201070/images/game/currentHeightIcon.png'
		},
		{
			name: 'speedBuff',
			type: 'png',
			id: 'cloud://cloud-i9tsg.636c-cloud-i9tsg-1302201070/images/game/prop/speedBuff.png'
		},
		{
			name: 'pauseButtonBg',
			type: 'png',
			id: 'cloud://cloud-i9tsg.636c-cloud-i9tsg-1302201070/images/game/暂停.png'
		},
		{
			name: 'indexButtonBg',
			type: 'png',
			id: 'cloud://cloud-i9tsg.636c-cloud-i9tsg-1302201070/images/game/首页.png'
		},
		{
			name: 'continueButtonBg',
			type: 'png',
			id: 'cloud://cloud-i9tsg.636c-cloud-i9tsg-1302201070/images/game/continueButton.png'
		},
		{
			name: 'textWinBg1',
			type: 'png',
			id: 'cloud://cloud-i9tsg.636c-cloud-i9tsg-1302201070/images/game/textWin/textWinBg1.png'
		},
		{
			name: 'textWinBg2',
			type: 'png',
			id: 'cloud://cloud-i9tsg.636c-cloud-i9tsg-1302201070/images/game/textWin/textWinBg2.png'
		},
		{
			name: 'textWinBg3',
			type: 'png',
			id: 'cloud://cloud-i9tsg.636c-cloud-i9tsg-1302201070/images/game/textWin/textWinBg3.png'
		},
		{
			name: 'textWinBg4',
			type: 'png',
			id: 'cloud://cloud-i9tsg.636c-cloud-i9tsg-1302201070/images/game/textWin/textWinBg4.png'
		},
		{
			name: 'chipFrame',
			type: 'png',
			id: 'cloud://cloud-i9tsg.636c-cloud-i9tsg-1302201070/images/game/textWin/chipFrame.png'
		},
		{
			name: 'chipLayoutBg',
			type: 'png',
			id: 'cloud://cloud-i9tsg.636c-cloud-i9tsg-1302201070/images/game/textWin/chipLayoutBg.png'
		},
		{
			name: 'blueBroken0',
			type: 'png',
			id: 'cloud://cloud-i9tsg.636c-cloud-i9tsg-1302201070/images/game/jelly/broken/blueBroken0.png'
		},
		{
			name: 'blueBroken1',
			type: 'png',
			id: 'cloud://cloud-i9tsg.636c-cloud-i9tsg-1302201070/images/game/jelly/broken/blueBroken1.png'
		},
		{
			name: 'blueBroken2',
			type: 'png',
			id: 'cloud://cloud-i9tsg.636c-cloud-i9tsg-1302201070/images/game/jelly/broken/blueBroken2.png'
		},
		{
			name: 'blueBroken3',
			type: 'png',
			id: 'cloud://cloud-i9tsg.636c-cloud-i9tsg-1302201070/images/game/jelly/broken/blueBroken3.png'
		},
		{
			name: 'appleBroken0',
			type: 'png',
			id: 'cloud://cloud-i9tsg.636c-cloud-i9tsg-1302201070/images/game/jelly/broken/appleBroken0.png'
		},
		{
			name: 'appleBroken1',
			type: 'png',
			id: 'cloud://cloud-i9tsg.636c-cloud-i9tsg-1302201070/images/game/jelly/broken/appleBroken1.png'
		},
		{
			name: 'appleBroken2',
			type: 'png',
			id: 'cloud://cloud-i9tsg.636c-cloud-i9tsg-1302201070/images/game/jelly/broken/appleBroken2.png'
		},
		{
			name: 'appleBroken3',
			type: 'png',
			id: 'cloud://cloud-i9tsg.636c-cloud-i9tsg-1302201070/images/game/jelly/broken/appleBroken3.png'
		},
		{
			name: 'orangeBroken0',
			type: 'png',
			id: 'cloud://cloud-i9tsg.636c-cloud-i9tsg-1302201070/images/game/jelly/broken/orangeBroken0.png'
		},
		{
			name: 'orangeBroken1',
			type: 'png',
			id: 'cloud://cloud-i9tsg.636c-cloud-i9tsg-1302201070/images/game/jelly/broken/orangeBroken1.png'
		},
		{
			name: 'orangeBroken2',
			type: 'png',
			id: 'cloud://cloud-i9tsg.636c-cloud-i9tsg-1302201070/images/game/jelly/broken/orangeBroken2.png'
		},
		{
			name: 'orangeBroken3',
			type: 'png',
			id: 'cloud://cloud-i9tsg.636c-cloud-i9tsg-1302201070/images/game/jelly/broken/orangeBroken3.png'
		},
		{
			name: 'melonBroken0',
			type: 'png',
			id: 'cloud://cloud-i9tsg.636c-cloud-i9tsg-1302201070/images/game/jelly/broken/melonBroken0.png'
		},
		{
			name: 'melonBroken1',
			type: 'png',
			id: 'cloud://cloud-i9tsg.636c-cloud-i9tsg-1302201070/images/game/jelly/broken/melonBroken1.png'
		},
		{
			name: 'melonBroken2',
			type: 'png',
			id: 'cloud://cloud-i9tsg.636c-cloud-i9tsg-1302201070/images/game/jelly/broken/melonBroken2.png'
		},
		{
			name: 'melonBroken3',
			type: 'png',
			id: 'cloud://cloud-i9tsg.636c-cloud-i9tsg-1302201070/images/game/jelly/broken/melonBroken3.png'
		},
		{
			name: 'berriesBroken0',
			type: 'png',
			id: 'cloud://cloud-i9tsg.636c-cloud-i9tsg-1302201070/images/game/jelly/broken/berriesBroken0.png'
		},
		{
			name: 'berriesBroken1',
			type: 'png',
			id: 'cloud://cloud-i9tsg.636c-cloud-i9tsg-1302201070/images/game/jelly/broken/berriesBroken1.png'
		},
		{
			name: 'berriesBroken2',
			type: 'png',
			id: 'cloud://cloud-i9tsg.636c-cloud-i9tsg-1302201070/images/game/jelly/broken/berriesBroken2.png'
		},
		{
			name: 'berriesBroken3',
			type: 'png',
			id: 'cloud://cloud-i9tsg.636c-cloud-i9tsg-1302201070/images/game/jelly/broken/berriesBroken3.png'
		},
		{
			name: 'peachBroken0',
			type: 'png',
			id: 'cloud://cloud-i9tsg.636c-cloud-i9tsg-1302201070/images/game/jelly/broken/peachBroken0.png'
		},
		{
			name: 'peachBroken1',
			type: 'png',
			id: 'cloud://cloud-i9tsg.636c-cloud-i9tsg-1302201070/images/game/jelly/broken/peachBroken1.png'
		},
		{
			name: 'peachBroken2',
			type: 'png',
			id: 'cloud://cloud-i9tsg.636c-cloud-i9tsg-1302201070/images/game/jelly/broken/peachBroken2.png'
		},
		{
			name: 'peachBroken3',
			type: 'png',
			id: 'cloud://cloud-i9tsg.636c-cloud-i9tsg-1302201070/images/game/jelly/broken/peachBroken3.png'
		},
		{
			name: 'helpCloseButtonBg',
			type: 'png',
			id: 'cloud://cloud-i9tsg.636c-cloud-i9tsg-1302201070/images/index/helpWin/helpCloseButtonBg.png'
		},
		{
			name: 'helpJelly',
			type: 'png',
			id: 'cloud://cloud-i9tsg.636c-cloud-i9tsg-1302201070/images/index/helpWin/helpJelly.png'
		},
		{
			name: 'helpWinBg',
			type: 'png',
			id: 'cloud://cloud-i9tsg.636c-cloud-i9tsg-1302201070/images/index/helpWin/helpWinBg.png'
		},
		{
			name: 'jellyFace_HZ',
			type: 'png',
			id: 'cloud://cloud-i9tsg.636c-cloud-i9tsg-1302201070/images/index/helpWin/jellyFace_HZ.png'
		},
		{
			name: 'jellyFace_WX',
			type: 'png',
			id: 'cloud://cloud-i9tsg.636c-cloud-i9tsg-1302201070/images/index/helpWin/jellyFace_WX.png'
		},
		{
			name: 'jellyFace_YS',
			type: 'png',
			id: 'cloud://cloud-i9tsg.636c-cloud-i9tsg-1302201070/images/index/helpWin/jellyFace_YS.png'
		},
		{
			name: 'jellyFace_ZX',
			type: 'png',
			id: 'cloud://cloud-i9tsg.636c-cloud-i9tsg-1302201070/images/index/helpWin/jellyFace_ZX.png'
		},
		{
			name: 'leftButtonBg',
			type: 'png',
			id: 'cloud://cloud-i9tsg.636c-cloud-i9tsg-1302201070/images/index/helpWin/leftButtonBg.png'
		},
		{
			name: 'rightButtonBg',
			type: 'png',
			id: 'cloud://cloud-i9tsg.636c-cloud-i9tsg-1302201070/images/index/helpWin/rightButtonBg.png'
		},
		{
			name: 'helpImg_CHPP',
			type: 'png',
			id: 'cloud://cloud-i9tsg.636c-cloud-i9tsg-1302201070/images/index/helpWin/helpImg/helpImg_CHPP.png'
		},
		{
			name: 'helpImg_CHXJ',
			type: 'png',
			id: 'cloud://cloud-i9tsg.636c-cloud-i9tsg-1302201070/images/index/helpWin/helpImg/helpImg_CHXJ.png'
		},
		{
			name: 'helpImg_GDPP',
			type: 'png',
			id: 'cloud://cloud-i9tsg.636c-cloud-i9tsg-1302201070/images/index/helpWin/helpImg/helpImg_GDPP.png'
		},
		{
			name: 'helpImg_HX',
			type: 'png',
			id: 'cloud://cloud-i9tsg.636c-cloud-i9tsg-1302201070/images/index/helpWin/helpImg/helpImg_HX.png'
		},
		{
			name: 'helpImg_HXXZ',
			type: 'png',
			id: 'cloud://cloud-i9tsg.636c-cloud-i9tsg-1302201070/images/index/helpWin/helpImg/helpImg_HXXZ.png'
		},
		{
			name: 'helpImg_LZJ',
			type: 'png',
			id: 'cloud://cloud-i9tsg.636c-cloud-i9tsg-1302201070/images/index/helpWin/helpImg/helpImg_LZJ.png'
		},
		{
			name: 'helpImg_NLZC',
			type: 'png',
			id: 'cloud://cloud-i9tsg.636c-cloud-i9tsg-1302201070/images/index/helpWin/helpImg/helpImg_NLZC.png'
		},
		{
			name: 'helpImg_NLZD',
			type: 'png',
			id: 'cloud://cloud-i9tsg.636c-cloud-i9tsg-1302201070/images/index/helpWin/helpImg/helpImg_NLZD.png'
		},
		{
			name: 'helpImg_ZAW',
			type: 'png',
			id: 'cloud://cloud-i9tsg.636c-cloud-i9tsg-1302201070/images/index/helpWin/helpImg/helpImg_ZAW.png'
		}
	]
}