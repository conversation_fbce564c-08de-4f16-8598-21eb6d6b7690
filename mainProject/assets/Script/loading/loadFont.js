var cloudData = require('cloudUrl');
cc.Class({
    extends: cc.Component,

    properties: {
        num: 0
    },

    onLoad(){
        if(CC_WECHATGAME){
            // 动态加载按钮字体
            if(this.num == 0){
                // 华文琥珀
                cc.loader.loadRes(cloudData.url.fontHWHP, cc.TTFFont, (err, cc_ttffont)=>{
                    if(err) return;
                    this.node.getComponent(cc.Label).font = cc_ttffont;
                });
            }else if(this.num ==1){
                // 方正幼圆
                cc.loader.loadRes(cloudData.url.fontFZYY, cc.TTFFont, (err, cc_ttffont)=>{
                    if(err) return;
                    this.node.getComponent(cc.Label).font = cc_ttffont;
                });
            }
        }
    }
});
