var cloudData = require('cloudUrl');
cc.Class({
    extends: cc.Component,

    properties: {

    },

    onLoad() {
        if(CC_WECHATGAME){
            this.getComponent(cc.Sprite).spriteFrame = new cc.SpriteFrame(cloudData.ccData[this.node.name]);
            // 九宫格分割
            if(this.node.name=='moreBg'){
				this.getComponent(cc.Sprite).spriteFrame.insetBottom = 53;
				this.getComponent(cc.Sprite).spriteFrame.insetLeft = 53;
				this.getComponent(cc.Sprite).spriteFrame.insetRight = 53;
				this.getComponent(cc.Sprite).spriteFrame.insetTop = 53;
            }
            if(this.node.name=='table'){
				this.getComponent(cc.Sprite).spriteFrame.insetBottom = 62;
				this.getComponent(cc.Sprite).spriteFrame.insetLeft = 13;
				this.getComponent(cc.Sprite).spriteFrame.insetRight = 15;
				this.getComponent(cc.Sprite).spriteFrame.insetTop = 0;
            }
            if(this.node.name=='textButtonBg'){
                this.getComponent(cc.Sprite).spriteFrame.insetBottom = 34;
                this.getComponent(cc.Sprite).spriteFrame.insetLeft = 41;
                this.getComponent(cc.Sprite).spriteFrame.insetRight = 41;
                this.getComponent(cc.Sprite).spriteFrame.insetTop = 34;
            }
            if(this.node.name=='topList'){
                this.getComponent(cc.Sprite).spriteFrame.insetBottom = 50;
                this.getComponent(cc.Sprite).spriteFrame.insetLeft = 50;
                this.getComponent(cc.Sprite).spriteFrame.insetRight = 50;
                this.getComponent(cc.Sprite).spriteFrame.insetTop = 50;
            }
            if(this.node.name=='topListBg'){
                this.getComponent(cc.Sprite).spriteFrame.insetBottom = 50;
                this.getComponent(cc.Sprite).spriteFrame.insetLeft = 50;
                this.getComponent(cc.Sprite).spriteFrame.insetRight = 50;
                this.getComponent(cc.Sprite).spriteFrame.insetTop = 50;
            }
            if(this.node.name=='topListBottom'){
                this.getComponent(cc.Sprite).spriteFrame.insetBottom = 50;
                this.getComponent(cc.Sprite).spriteFrame.insetLeft = 50;
                this.getComponent(cc.Sprite).spriteFrame.insetRight = 50;
                this.getComponent(cc.Sprite).spriteFrame.insetTop = 50;
            }
            if(this.node.name=='sliderBg'){
                this.getComponent(cc.Sprite).spriteFrame.insetBottom = 20;
                this.getComponent(cc.Sprite).spriteFrame.insetLeft = 20;
                this.getComponent(cc.Sprite).spriteFrame.insetRight = 20;
                this.getComponent(cc.Sprite).spriteFrame.insetTop = 20;
            }
            if(this.node.name=='sliderBar'){
                this.getComponent(cc.Sprite).spriteFrame.insetBottom = 15;
                this.getComponent(cc.Sprite).spriteFrame.insetLeft = 15;
                this.getComponent(cc.Sprite).spriteFrame.insetRight = 15;
                this.getComponent(cc.Sprite).spriteFrame.insetTop = 15;
            }
            if(this.node.name=='chipLayoutBg'){
                this.getComponent(cc.Sprite).spriteFrame.insetBottom = 30;
                this.getComponent(cc.Sprite).spriteFrame.insetLeft = 30;
                this.getComponent(cc.Sprite).spriteFrame.insetRight = 30;
                this.getComponent(cc.Sprite).spriteFrame.insetTop = 30;
            }
            if(this.node.name=='textWinBg1'){
                this.getComponent(cc.Sprite).spriteFrame.insetBottom = 80;
                this.getComponent(cc.Sprite).spriteFrame.insetLeft = 80;
                this.getComponent(cc.Sprite).spriteFrame.insetRight = 80;
                this.getComponent(cc.Sprite).spriteFrame.insetTop = 80;
            }
            if(this.node.name=='textWinBg2'){
                this.getComponent(cc.Sprite).spriteFrame.insetBottom = 80;
                this.getComponent(cc.Sprite).spriteFrame.insetLeft = 80;
                this.getComponent(cc.Sprite).spriteFrame.insetRight = 80;
                this.getComponent(cc.Sprite).spriteFrame.insetTop = 80;
            }
            if(this.node.name=='textWinBg3'){
                this.getComponent(cc.Sprite).spriteFrame.insetBottom = 80;
                this.getComponent(cc.Sprite).spriteFrame.insetLeft = 80;
                this.getComponent(cc.Sprite).spriteFrame.insetRight = 80;
                this.getComponent(cc.Sprite).spriteFrame.insetTop = 80;
            }
            if(this.node.name=='textWinBg4'){
                this.getComponent(cc.Sprite).spriteFrame.insetBottom = 80;
                this.getComponent(cc.Sprite).spriteFrame.insetLeft = 80;
                this.getComponent(cc.Sprite).spriteFrame.insetRight = 80;
                this.getComponent(cc.Sprite).spriteFrame.insetTop = 80;
            }
            if(this.node.name=='helpWinBg'){
                this.getComponent(cc.Sprite).spriteFrame.insetBottom = 100;
                this.getComponent(cc.Sprite).spriteFrame.insetLeft = 100;
                this.getComponent(cc.Sprite).spriteFrame.insetRight = 100;
                this.getComponent(cc.Sprite).spriteFrame.insetTop = 100;
            }
        }
    },


});
