var userData = require('userData');
cc.Class({
    extends: cc.Component,

    properties: {
        scrollView: cc.ScrollView,
        view: cc.Node,
        content: cc.Node,
        layout: cc.Layout,
        sideButton: cc.Node,
        unlockLabel: cc.Node
    },

    onLoad(){
        // 滚动区左右边距
        this.layout.paddingLeft = cc.winSize.width/2;
        this.layout.paddingRight = cc.winSize.width/2;
        // 缩放倍数
        this.scaleNum = 1.5;
        // 节点距离
        this.spaX = this.layout.spacingX;
        // 节点宽度
        this.Width = this.content.children[0].width;
        // 当前居中节点索引
        this.currentNode = 0;
        // 初始滚动位置
        setTimeout(()=>{
            this.scrollView.scrollToOffset(cc.v2(this.spaX*(parseInt(this.currentNode)+1)+this.Width*this.currentNode+this.Width*(this.scaleNum-1)/2,0));
            this.changePos();
        },100)
        // 松开拖动监听
        this.view.on('touchend',(event)=>{
            this.changePos();
            // 滚动
            this.scrollView.scrollToOffset(cc.v2(this.spaX*(parseInt(this.currentNode)+1)+this.Width*this.currentNode+this.Width*(this.scaleNum-1)/2,0),1,true);
        });
    },

    scorllEvent(){
        // 防止重复触发
        clearTimeout(this.eventT);
        this.eventT = setTimeout(()=>{
            this.changePos();
        },100);
    },

    changePos(){
        var posX = -this.content.x;
        for(var i in this.content.children){
            var contChild = this.content.children[i];
            var sideChild = this.sideButton.children[i];
            if(contChild.x-contChild.width*contChild.scale/2-this.spaX/2<posX && posX<=contChild.x+contChild.width*contChild.scale/2+this.spaX/2){
                contChild.opacity = 255;
                this.currentNode = i;
                contChild.runAction(cc.scaleTo(0.3,this.scaleNum));
                // 侧边按钮
                sideChild.runAction(cc.scaleTo(0.3,1,1));
                sideChild.getComponent(cc.Button).normalColor = cc.color(255,255,255);
                // 解锁按钮
                if(userData.skin.skinLock[contChild.name] == 1){
                    this.unlockLabel.getComponent(cc.Label).string = contChild.name==userData.skin.currentSkin ? '已使用' : '使 用';
                }else{
                    this.unlockLabel.getComponent(cc.Label).string = '解 锁';
                    // 侧边锁
                    sideChild.getChildByName('lock').opacity = 255;
                }
                
            }else{
                contChild.opacity = 150;
                contChild.runAction(cc.scaleTo(0.2,1));
                // 侧边按钮
                sideChild.runAction(cc.scaleTo(0.3,0.8,1));
                sideChild.getComponent(cc.Button).normalColor = cc.color(200,200,200);
                // 侧边锁
                sideChild.getChildByName('lock').opacity = 0;

            }
        }
    },

    clickSideButton(event,customEventData){
        this.currentNode = customEventData;
        // 滚动
        this.scrollView.scrollToOffset(cc.v2(this.spaX*(parseInt(this.currentNode)+1)+this.Width*this.currentNode+this.Width*(this.scaleNum-1)/2,0),0.5,true);
        setTimeout(()=>{this.changePos()},300);
    }
});
