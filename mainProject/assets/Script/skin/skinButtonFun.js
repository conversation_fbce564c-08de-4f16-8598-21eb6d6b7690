var userData = require('userData');
cc.Class({
    extends: cc.Component,

    properties: {
        unlockLabel: cc.Label,
        content: cc.Node
    },

    backButton(){
        if(CC_WECHATGAME){
            // 播放音效
            var audios = cc.find('audios').getComponent('audios');
            audios.playEffect(audios.effect_click);
        }
        // 全部加载成功后切换场景
        // 淡出
            cc.find('Canvas').runAction(
                cc.sequence(
                    cc.fadeOut(0.5),cc.callFunc(()=>{
                        cc.director.loadScene("index");
                    })
                )
            )
    },

    unlockButton(){
        if(CC_WECHATGAME){
            // 播放音效
            var audios = cc.find('audios').getComponent('audios');
            audios.playEffect(audios.effect_click);

            let scorllEventJs = cc.find('Canvas/skinContent/fourBoxList').getComponent('scorllEvent');
            let currentName = this.content.children[scorllEventJs.currentNode].name;
            let skinChip = userData.skin.skinChip[currentName];
            switch(this.unlockLabel.string){
                case '使 用':
                    // 解锁未完成不能使用
                    if(this.doUnlock) return;
                    var preName = userData.skin.currentSkin;
                    this.unlockLabel.string = '已使用';
                    userData.skin.currentSkin = currentName;
                    wx.cloud.callFunction({
                        name:'changeCurSkin',
                        data: {
                            name: currentName
                        },
                        success: res=>{
                            console.log('使用成功');
                        },
                        fail: err=>{
                            console.log('使用失败');
                            // 退回操作
                            userData.skin.currentSkin = preName;
                        }
                    });
                    break;
                case '解 锁':
                    for(var i in skinChip){
                        if(skinChip[i]==0){
                            console.log('碎片不够')
                            return;
                        }
                    }
                    // 开始解锁状态
                    this.doUnlock = true;
                    this.unlockLabel.string = '使 用';
                    userData.skin.skinLock[currentName] = 1;
                    wx.cloud.callFunction({
                        name:'unlockSkin',
                        data: {
                            name: currentName
                        },
                        success: res=>{
                            // 结束解锁状态
                            this.doUnlock = false;
                            console.log('解锁成功');
                        },
                        fail: err=>{
                            console.log('解锁失败');
                            // 退回操作
                            userData.skin.skinLock[currentName] = 0;
                            // 结束解锁状态
                            this.doUnlock = false;
                        }
                    });
                    break;
                case '已使用':
                    break;
            }
        }
    }
});
