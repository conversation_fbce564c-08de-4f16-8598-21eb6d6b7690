var cloudData = require('cloudUrl');
var userData = require('userData');
cc.Class({
    extends: cc.Component,

    properties: {
        content: cc.Node
    },

    onload() {
        //当屏幕大小发生改变时调用
        cc.view.setResizeCallback(()=>{
            this.node.width = cc.winSize.width;
            this.node.height = cc.winSize.height;
        });
    },

    start(){
        if(CC_WECHATGAME){
            this.updateSkin();
            // 刷新皮肤数据
            wx.cloud.callFunction({
                name:'getSkin',
                complete:res=>{
                    // 皮肤碎片
                    userData.skin.skinChip = res.result.skinChip;
                    // 皮肤解锁
                    userData.skin.skinLock = res.result.skinLock;
                    // 当前皮肤
                    userData.skin.currentSkin = res.result.currentSkin;
                    // 更新页面
                    this.updateSkin();
                }
            })
            // 动态加载按钮贴图
            var unlockButton = cc.find('Canvas/ui/unlockButton').getComponent(cc.Button);
            unlockButton.normalSprite = new cc.SpriteFrame(cloudData.ccData.textButtonBg);
            unlockButton.normalSprite.insetBottom = 34;
            unlockButton.normalSprite.insetLeft = 41;
            unlockButton.normalSprite.insetRight = 41;
            unlockButton.normalSprite.insetTop = 34;

            unlockButton.pressedSprite = new cc.SpriteFrame(cloudData.ccData.textButtonBgDark);
            unlockButton.pressedSprite.insetBottom = 34;
            unlockButton.pressedSprite.insetLeft = 41;
            unlockButton.pressedSprite.insetRight = 41;
            unlockButton.pressedSprite.insetTop = 34;

            var backButton = cc.find('Canvas/ui/backButton').getComponent(cc.Button)
            backButton.normalSprite = new cc.SpriteFrame(cloudData.ccData.backButtonBg);
            backButton.pressedSprite = new cc.SpriteFrame(cloudData.ccData.backButtonBgDark);

            // 播放背景音乐
            var audios = cc.find('audios').getComponent('audios');
            audios.changeBgm(audios.bgm2);
        }
    },
    updateSkin(){
        // 碎片亮暗
        for(let skinName in userData.skin.skinChip){
            for(let chipName in userData.skin.skinChip[skinName]){
                this.content.getChildByName(skinName).getChildByName('fourBox').getChildByName(chipName).color = userData.skin.skinChip[skinName][chipName] ? cc.color(255,255,255) : cc.color(200,200,200)
            }
        }
    }
});
