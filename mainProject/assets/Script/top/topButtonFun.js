var cloudData = require('cloudUrl');
cc.Class({
    extends: cc.Component,

    properties: {
        wxSubContext: cc.Node,
        friendBT: cc.Button,
        groupBT: cc.Button,
        heightBT: cc.Button,
        scoreBT: cc.Button,
        textWin: cc.Node
    },

    start(){
        this.friendBTSp = this.friendBT.normalSprite;
        this.groupBTSp = this.groupBT.normalSprite;
        this.heightBTSp = this.heightBT.normalSprite;
        this.scoreBTSp = this.scoreBT.normalSprite;
        // 默认朋友-高度
        this.groupType = 'Friend';
        this.dataType = 'Height';
        // 选中状态贴图
        this.friendBT.normalSprite = this.friendBT.pressedSprite;
        this.heightBT.normalSprite = this.heightBT.pressedSprite;
    },

    backButton(){
        if(CC_WECHATGAME){
            // 播放音效
            var audios = cc.find('audios').getComponent('audios');
            audios.playEffect(audios.effect_click);
        }
        // 全部加载成功后切换场景
        // 淡出
        cc.find('Canvas').runAction(
            cc.sequence(
                cc.fadeOut(0.5),cc.callFunc(()=>{
                    cc.director.loadScene("index");
                })
            )
        )
    },

    friendButton(){
        if(CC_WECHATGAME){
            // 播放音效
            var audios = cc.find('audios').getComponent('audios');
            audios.playEffect(audios.effect_click);
        
            if(this.groupType == 'Friend'){
                // 第二次点击只刷新
                this.wxSubContext.getComponent('wxSubContext').refreshTop();
            }else{
                this.groupType = 'Friend';
                this.friendBT.normalSprite = this.friendBT.pressedSprite;
                this.groupBT.normalSprite = this.groupBTSp;
                wx.postMessage({
                    msg: 'get'+this.groupType+this.dataType,
                    shareTicket: wx.getLaunchOptionsSync().shareTicket
                });
                this.wxSubContext.getComponent('wxSubContext').refreshTop();
            }
        }
    },

    groupButton(){
        if(CC_WECHATGAME){
            // 播放音效
            var audios = cc.find('audios').getComponent('audios');
            audios.playEffect(audios.effect_click);

            console.log(wx.getLaunchOptionsSync())
            if(wx.getLaunchOptionsSync().shareTicket){
                if(this.groupType == 'Group'){
                    // 第二次点击只刷新
                    this.wxSubContext.getComponent('wxSubContext').refreshTop();
                }else{
                    this.groupType = 'Group';
                    this.groupBT.normalSprite = this.groupBT.pressedSprite;
                    this.friendBT.normalSprite = this.friendBTSp;
                    wx.postMessage({
                        msg: 'get'+this.groupType+this.dataType,
                        shareTicket: wx.getLaunchOptionsSync().shareTicket
                    });
                    this.wxSubContext.getComponent('wxSubContext').refreshTop();
                }
            }else{
                console.log('没有shareTicket')
                // 文字弹窗
                this.textWin.active = true;
                this.textWin.getChildByName('textWinBg1').runAction(
                    cc.spawn(
                        cc.scaleTo(0.2,1,1),cc.fadeIn(0.3)
                    )
                );
                this.textWin.getChildByName('grayBg').runAction(cc.fadeTo(0.2,80));
            }
        }
    },

    heightButton(){
        if(CC_WECHATGAME){
            // 播放音效
            var audios = cc.find('audios').getComponent('audios');
            audios.playEffect(audios.effect_click);

            if(this.dataType == 'Height'){
                // 第二次点击只刷新
                this.wxSubContext.getComponent('wxSubContext').refreshTop();
            }else{
                this.dataType = 'Height';
                this.heightBT.normalSprite = this.heightBT.pressedSprite;
                this.scoreBT.normalSprite = this.scoreBTSp;
                wx.postMessage({
                    msg: 'get'+this.groupType+this.dataType,
                    shareTicket: wx.getLaunchOptionsSync().shareTicket
                });
                this.wxSubContext.getComponent('wxSubContext').refreshTop(); 
            }
        }
    },

    scoreButton(){
        if(CC_WECHATGAME){
            // 播放音效
            var audios = cc.find('audios').getComponent('audios');
            audios.playEffect(audios.effect_click);

            if(this.dataType == 'Score'){
                // 第二次点击只刷新
                this.wxSubContext.getComponent('wxSubContext').refreshTop();
            }else{
                this.dataType = 'Score';
                this.scoreBT.normalSprite = this.scoreBT.pressedSprite;
                this.heightBT.normalSprite = this.heightBTSp;
                wx.postMessage({
                    msg: 'get'+this.groupType+this.dataType,
                    shareTicket: wx.getLaunchOptionsSync().shareTicket
                });
                this.wxSubContext.getComponent('wxSubContext').refreshTop();
            }
        }
    }
});
