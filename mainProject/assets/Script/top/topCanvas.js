var cloudData = require('cloudUrl');
cc.Class({
    extends: cc.Component,

    properties: {
        wxSubContext: cc.Node
    },

    onload() {
        //当屏幕大小发生改变时调用
        cc.view.setResizeCallback(()=>{
            this.node.width = cc.winSize.width;
            this.node.height = cc.winSize.height;
        });
    },

    start(){
        if(CC_WECHATGAME){
            // 初始化排行榜
            this.wxSubContext.getComponent('wxSubContext').initTop();
            this.wxSubContext.getComponent('wxSubContext').refreshTop();
            // 动态加载按钮贴图
            var backButton = cc.find('Canvas/ui/backButton').getComponent(cc.Button)
            backButton.normalSprite = new cc.SpriteFrame(cloudData.ccData.backButtonBg);
            backButton.pressedSprite = new cc.SpriteFrame(cloudData.ccData.backButtonBgDark);

            var heightButton = cc.find('Canvas/ui/heightButton').getComponent(cc.Button)
            heightButton.normalSprite = new cc.SpriteFrame(cloudData.ccData.heightButtonBg);
            heightButton.pressedSprite = new cc.SpriteFrame(cloudData.ccData.heightButtonBgDark);


            var scoreButton = cc.find('Canvas/ui/scoreButton').getComponent(cc.Button)
            scoreButton.normalSprite = new cc.SpriteFrame(cloudData.ccData.scoreButtonBg);
            scoreButton.pressedSprite = new cc.SpriteFrame(cloudData.ccData.scoreButtonBgDark);

            var friendButton = cc.find('Canvas/ui/topRight/topListButton/friendButton').getComponent(cc.Button);
            friendButton.normalSprite = new cc.SpriteFrame(cloudData.ccData.textButtonBg);
            friendButton.normalSprite.insetBottom = 34;
            friendButton.normalSprite.insetLeft = 41;
            friendButton.normalSprite.insetRight = 41;
            friendButton.normalSprite.insetTop = 34;
            
            friendButton.pressedSprite = new cc.SpriteFrame(cloudData.ccData.textButtonBgDark);
            friendButton.pressedSprite.insetBottom = 34;
            friendButton.pressedSprite.insetLeft = 41;
            friendButton.pressedSprite.insetRight = 41;
            friendButton.pressedSprite.insetTop = 34;

            var groupButton = cc.find('Canvas/ui/topRight/topListButton/groupButton').getComponent(cc.Button);
            groupButton.normalSprite = new cc.SpriteFrame(cloudData.ccData.textButtonBg);
            groupButton.normalSprite.insetBottom = 34;
            groupButton.normalSprite.insetLeft = 41;
            groupButton.normalSprite.insetRight = 41;
            groupButton.normalSprite.insetTop = 34;

            groupButton.pressedSprite = new cc.SpriteFrame(cloudData.ccData.textButtonBgDark);
            groupButton.pressedSprite.insetBottom = 34;
            groupButton.pressedSprite.insetLeft = 41;
            groupButton.pressedSprite.insetRight = 41;
            groupButton.pressedSprite.insetTop = 34;

            // 播放背景音乐
            var audios = cc.find('audios').getComponent('audios');
            audios.changeBgm(audios.bgm2);
        }
    }
});
