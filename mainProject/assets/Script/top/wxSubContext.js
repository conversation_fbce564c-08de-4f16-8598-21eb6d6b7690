cc.Class({
    extends: cc.Component,

    properties: {
    	wxSub: cc.Node,
        wxSub2: cc.Node
    },

    start(){
    	// 排行榜列表拉伸
        this.wxSub.height = (cc.winSize.width-430)/1571*1209;
        this.wxSub2.scale = (cc.winSize.width-430)/1571+0.2;
    },

	/**
     * 初始化排行榜
     */
    initTop(){
        if(CC_WECHATGAME){
        	wx.postMessage({
                msg: 'getFriendHeight'
            });
    		this.showOwnData();
    		setTimeout(()=>{
    			this.showListData();
    		},100);
        }
    },

    /**
     * 间歇刷新排行榜及底部自己信息
     */
    refreshTop(){
    	if(CC_WECHATGAME){
    		clearTimeout(this.t1);
    		clearTimeout(this.t2);
            clearTimeout(this.t3);
    		clearTimeout(this.t4);
			this.t1 = setTimeout(()=>{
				this.showOwnData();
    		},200);
    		this.t2 = setTimeout(()=>{
				this.showListData();
    		},400);
    		this.t3 = setTimeout(()=>{
				this.showOwnData();
    		},600);
    		this.t4 = setTimeout(()=>{
				this.showListData();
    		},800);
        }
    },

    /**
     * 刷新排行榜
     */
    showListData(){
    	// 底部自己信息停止刷新
    	this.wxSub2.getComponent(cc.WXSubContextView).enabled = false;
    	// 排行榜开始刷新
        this.wxSub.getComponent(cc.WXSubContextView).enabled = true;
        this.wxSub.getComponent(cc.WXSubContextView).reset();
        // 子域更新数据
        wx.postMessage({
            msg: 'showListData'
        });
    },

    /**
     * 刷新底部自己信息
     */
    showOwnData(){
    	// 排行榜停止刷新
    	this.wxSub.getComponent(cc.WXSubContextView).enabled = false;
    	// 底部自己信息开始刷新
        this.wxSub2.getComponent(cc.WXSubContextView).enabled = true;
        this.wxSub2.getComponent(cc.WXSubContextView).reset();
        // 子域更新数据
        wx.postMessage({
            msg: 'showOwnData'
        });
    },

    /**
     * 停止刷新子域
     */
    stopRefresh(){
    	this.wxSub2.getComponent(cc.WXSubContextView).enabled = false;
        this.wxSub.getComponent(cc.WXSubContextView).enabled = false;
    }
});
