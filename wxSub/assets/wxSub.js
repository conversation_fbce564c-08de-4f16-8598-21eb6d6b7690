cc.Class({
    extends: cc.Component,

    properties: {
        content: cc.Node,
        content2: cc.Node
    },

    start(){
        this.userInfo = null;
        // 获取自己数据
        this.getUserInfo();
        wx.onMessage(data=>{
            // 加载贴图
            if(data.msg === 'loadImg'){
                // 列表背景
                cc.loader.load(data.topList,(err,ccData)=>{
                    if(err) return;
                    for(let i in this.content.children){
                        this.content.children[i].getComponent(cc.Sprite).spriteFrame = new cc.SpriteFrame(ccData);
                        this.content.children[i].getComponent(cc.Sprite).spriteFrame.insetBottom = 50;
                        this.content.children[i].getComponent(cc.Sprite).spriteFrame.insetLeft = 50;
                        this.content.children[i].getComponent(cc.Sprite).spriteFrame.insetRight = 50;
                        this.content.children[i].getComponent(cc.Sprite).spriteFrame.insetTop = 50;
                    }
                });
                // 冠军图标
                cc.loader.load(data.firstIcon,(err,ccData)=>{
                    if(err) return;
                        this.content.children[0].getChildByName('listContent').getChildByName('firstIcon').getComponent(cc.Sprite).spriteFrame = new cc.SpriteFrame(ccData);
                });
                // 亚军图标
                cc.loader.load(data.secondIcon,(err,ccData)=>{
                    if(err) return;
                        this.content.children[1].getChildByName('listContent').getChildByName('secondIcon').getComponent(cc.Sprite).spriteFrame = new cc.SpriteFrame(ccData);
                });
                // 季军图标
                cc.loader.load(data.thirdIcon,(err,ccData)=>{
                    if(err) return;
                        this.content.children[2].getChildByName('listContent').getChildByName('thirdIcon').getComponent(cc.Sprite).spriteFrame = new cc.SpriteFrame(ccData);
                });
                // 分数图标
                cc.loader.load(data.scoreIcon,(err,ccData)=>{
                    if(err) return;
                    this.scoreIcon = ccData;
                    this.loadScoreIcon();
                });
                // 高度图标
                cc.loader.load(data.heightIcon,(err,ccData)=>{
                    if(err) return;
                    this.heightIcon = ccData;
                });
            }
            // 上传分数
            if(data.msg === 'uploadScore'){
                wx.getUserCloudStorage({
                    keyList:['maxScore'],
                    success: res=>{
                        if(res.KVDataList.length == 0){
                            // 首次创建分数
                            let KVDataList = new Array();
                            KVDataList.push({key:'maxScore', value: data.score});
                            wx.setUserCloudStorage({KVDataList: KVDataList})
                        }else if(res.KVDataList[0] && res.KVDataList[0].key=='maxScore'){
                            // 若上传分数大于记录 则更新分数
                            if(parseInt(res.KVDataList[0].value)<parseInt(data.score)){
                                let KVDataList = new Array();
                                KVDataList.push({key:'maxScore', value: data.score});
                                wx.setUserCloudStorage({KVDataList: KVDataList})
                            }
                        }
                    }
                });
                
            }
            // 上传高度
            if(data.msg === 'uploadHeight'){
                wx.getUserCloudStorage({
                    keyList:['maxHeight'],
                    success: res=>{
                        if(res.KVDataList.length == 0){
                            // 首次创建高度
                            let KVDataList = new Array();
                            KVDataList.push({key:'maxHeight', value: data.height});
                            wx.setUserCloudStorage({KVDataList: KVDataList})
                        }else if(res.KVDataList[0] && res.KVDataList[0].key=='maxHeight'){
                            // 若上传高度大于记录 则更新高度
                            if(parseInt(res.KVDataList[0].value)<parseInt(data.height)){
                                let KVDataList = new Array();
                                KVDataList.push({key:'maxHeight', value: data.height});
                                wx.setUserCloudStorage({KVDataList: KVDataList})
                            }
                        }
                    }
                });
            }
            // 显示自己数据
            if(data.msg === 'showOwnData'){
                this.content.active = false;
                this.content2.active = true;
            }
            // 显示列表数据
            if(data.msg === 'showListData'){
                this.content.active = true;
                this.content2.active = false;
            }
            // 获取朋友-分数
            if(data.msg === 'getFriendScore'){
                this.getFriend('maxScore');
            }
            // 获取朋友-高度
            if(data.msg === 'getFriendHeight'){
                this.getFriend('maxHeight');
            }
            // 获取群组-分数
            if(data.msg === 'getGroupScore'){
                this.getGroup('maxScore',data.shareTicket);
            }
            // 获取群组-高度
            if(data.msg === 'getGroupHeight'){
                this.getGroup('maxHeight',data.shareTicket);
            }
        })
    },
    /**
     * 更换分数图标   
     */
    loadScoreIcon(){
        if(!this.scoreIcon) return;
        for(let i in this.content.children){
            this.content.children[i].getChildByName('listContent').getChildByName('typeIcon').getComponent(cc.Sprite).spriteFrame = new cc.SpriteFrame(this.scoreIcon);
        }
        this.content2.getChildByName('listContent').getChildByName('typeIcon').getComponent(cc.Sprite).spriteFrame = new cc.SpriteFrame(this.scoreIcon);
    },
    /**
     * 更换高度图标   
     */
    loadHeightIcon(){
        if(!this.heightIcon) return;
        for(let i in this.content.children){
            this.content.children[i].getChildByName('listContent').getChildByName('typeIcon').getComponent(cc.Sprite).spriteFrame = new cc.SpriteFrame(this.heightIcon);
        }
        this.content2.getChildByName('listContent').getChildByName('typeIcon').getComponent(cc.Sprite).spriteFrame = new cc.SpriteFrame(this.heightIcon);
    },
    /**
     * 获取朋友关系链数据    
     */
    getFriend(type){
        // 更换图标
        if(type == 'maxScore'){
            this.loadScoreIcon();
        }else if(type == 'maxHeight'){
            this.loadHeightIcon();
        }

        // 隐藏所有数据
        for(var i in this.content.children){
            this.content.children[i].getChildByName('listContent').active = false;
        }
        this.content2.getChildByName('listContent').active = false;

        // 获取信息
        wx.getFriendCloudStorage({
            keyList:[type],
            success: res=>{
                // 冒泡排序
                let arr = res.data;
                // 删除没有数据的用户
                for(var i=0; i<arr.length; i++){
                    if(arr[i].KVDataList.length == 0){
                        arr.splice(i,1);
                        i--;
                    }
                }
                let len = arr.length;
                // 排行榜无数据
                if(len==0) return;
                for(var i=0; i<len-1; i++){
                    for(var j=0; j<len-1-i; j++){
                        // 相邻元素两两对比，元素交换，小的元素交换到后面
                        if(parseInt(arr[j].KVDataList[0].value) < parseInt(arr[j+1].KVDataList[0].value)){
                            var temp = arr[j];
                            arr[j] = arr[j+1];
                            arr[j+1] = temp;
                        }
                    }
                }
                // 更新自己数据
                // 初始化无排名无数值
                this.content2.getChildByName('listContent').getChildByName('rank').getComponent(cc.Label).string = '0';
                this.content2.getChildByName('listContent').getChildByName('number').getComponent(cc.Label).string = '0';
                // 若已获取到用户数据
                if(this.userInfo){
                    for(var i=0; i<len; i++){
                        if(arr[i].avatarUrl == this.userInfo.avatarUrl){
                            // 排名
                            this.content2.getChildByName('listContent').getChildByName('rank').getComponent(cc.Label).string = String(i+1);
                            // 数值
                            this.content2.getChildByName('listContent').getChildByName('number').getComponent(cc.Label).string = arr[i].KVDataList[0].value;
                            break;
                        }
                    }
                    this.content2.getChildByName('listContent').active = true;
                }              
                
                // 更新排行榜数据
                for(let i=0; i<Math.min(10,len); i++){
                    // 头像
                    let image = wx.createImage();
                    image.onload = ()=>{
                        let texture = new cc.Texture2D();
                        texture.initWithElement(image);
                        texture.handleLoadedTexture();
                        this.content.children[i].getChildByName('listContent').getChildByName('avatar').getComponent(cc.Sprite).spriteFrame = new cc.SpriteFrame(texture);
                    };
                    image.src = arr[i].avatarUrl;
                    // 昵称
                    this.content.children[i].getChildByName('listContent').getChildByName('name').getComponent(cc.Label).string = arr[i].nickname;
                    // 数值
                    this.content.children[i].getChildByName('listContent').getChildByName('number').getComponent(cc.Label).string = arr[i].KVDataList[0].value;
                    // 显示数据
                    this.content.children[i].getChildByName('listContent').active = true;
                }
            }
        });
    },
    /**
     * 获取群组关系链数据    
     */
    getGroup(type,shareTicket){
        // 更换图标
        if(type == 'maxScore'){
            this.loadScoreIcon();
        }else if(type == 'maxHeight'){
            this.loadHeightIcon();
        }

        // 隐藏所有数据
        for(var i in this.content.children){
            this.content.children[i].getChildByName('listContent').active = false;
        }
        this.content2.getChildByName('listContent').active = false;

        // 获取信息
        wx.getGroupCloudStorage({
            shareTicket: shareTicket,
            keyList:[type],
            success: res=>{
                // 冒泡排序
                let arr = res.data;
                // 删除没有数据的用户
                for(var i=0; i<arr.length; i++){
                    if(arr[i].KVDataList.length == 0){
                        arr.splice(i,1);
                        i--;
                    }
                }
                let len = arr.length;
                // 排行榜无数据
                if(len==0) return;
                for(var i=0; i<len-1; i++){
                    for(var j=0; j<len-1-i; j++){
                        // 相邻元素两两对比，元素交换，小的元素交换到后面
                        if(parseInt(arr[j].KVDataList[0].value) < parseInt(arr[j+1].KVDataList[0].value)){
                            var temp = arr[j];
                            arr[j] = arr[j+1];
                            arr[j+1] = temp;
                        }
                    }
                }
                // 更新自己数据
                // 初始化无排名无数值
                this.content2.getChildByName('listContent').getChildByName('rank').getComponent(cc.Label).string = '0';
                this.content2.getChildByName('listContent').getChildByName('number').getComponent(cc.Label).string = '0';
                // 若已获取到用户数据
                if(this.userInfo){
                    for(var i=0; i<len; i++){
                        if(arr[i].avatarUrl == this.userInfo.avatarUrl){
                            // 排名
                            this.content2.getChildByName('listContent').getChildByName('rank').getComponent(cc.Label).string = String(i+1);
                            // 数值
                            this.content2.getChildByName('listContent').getChildByName('number').getComponent(cc.Label).string = arr[i].KVDataList[0].value;
                            break;
                        }
                    }
                    this.content2.getChildByName('listContent').active = true;
                }              
                
                // 更新排行榜数据
                for(let i=0; i<Math.min(10,len); i++){
                    // 头像
                    let image = wx.createImage();
                    image.onload = ()=>{
                        let texture = new cc.Texture2D();
                        texture.initWithElement(image);
                        texture.handleLoadedTexture();
                        this.content.children[i].getChildByName('listContent').getChildByName('avatar').getComponent(cc.Sprite).spriteFrame = new cc.SpriteFrame(texture);
                    };
                    image.src = arr[i].avatarUrl;
                    // 昵称
                    this.content.children[i].getChildByName('listContent').getChildByName('name').getComponent(cc.Label).string = arr[i].nickname;
                    // 数值
                    this.content.children[i].getChildByName('listContent').getChildByName('number').getComponent(cc.Label).string = arr[i].KVDataList[0].value;
                    // 显示数据
                    this.content.children[i].getChildByName('listContent').active = true;
                }
            }
        });
    },
    /**
     * 获取用户自身信息
     */
    getUserInfo(){
        // 获取自己数据
        wx.getUserInfo({
            openIdList: ['selfOpenId'],
            success: res=>{
                this.userInfo = res.data[0];
                // 设置用户头像
                let image = wx.createImage();
                image.onload = ()=>{
                    let texture = new cc.Texture2D();
                    texture.initWithElement(image);
                    texture.handleLoadedTexture();
                    this.content2.getChildByName('listContent').getChildByName('avatar').getComponent(cc.Sprite).spriteFrame = new cc.SpriteFrame(texture);
                };
                image.src = this.userInfo.avatarUrl;
                // 设置用户昵称
                this.content2.getChildByName('listContent').getChildByName('name').getComponent(cc.Label).string = this.userInfo.nickName;
            }
        });
    }
});
