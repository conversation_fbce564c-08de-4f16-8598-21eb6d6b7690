{"group-list": ["default"], "collision-matrix": [[true]], "excluded-modules": ["Audio", "AudioSource", "Action", "Animation", "<PERSON><PERSON>", "Collider", "Dynamic Atlas", "DragonBones", "EditBox", "Graphics", "<PERSON><PERSON><PERSON>", "Intersection", "Label Effect", "Mask", "<PERSON><PERSON>", "MotionStreak", "NodePool", "Native Socket", "Physics", "<PERSON><PERSON><PERSON><PERSON>", "PageViewIndicator", "ProgressBar", "ParticleSystem", "RichText", "Renderer Texture", "Slide<PERSON>", "<PERSON><PERSON>Bar", "ScrollView", "Spine Skeleton", "StudioComponent", "Toggle", "TiledMap", "VideoPlayer", "WebView", "3D", "3D Primitive", "3D Physics/cannon.js", "3D Physics/Builtin", "3D Particle"], "last-module-event-record-time": 1592649356193, "design-resolution-width": 1571, "design-resolution-height": 1209, "fit-width": false, "fit-height": true, "use-project-simulator-setting": false, "simulator-orientation": false, "use-customize-simulator": true, "simulator-resolution": {"height": 640, "width": 960}, "assets-sort-type": "name", "facebook": {"appID": "", "audience": {"enable": false}, "enable": false, "live": {"enable": false}}, "migrate-history": [], "start-scene": "current"}